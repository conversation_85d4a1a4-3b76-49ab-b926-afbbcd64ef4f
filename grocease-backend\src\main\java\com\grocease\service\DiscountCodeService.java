package com.grocease.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.grocease.dto.PaginatedResponse;
import com.grocease.dto.discount.*;
import com.grocease.entity.*;
import com.grocease.exception.BadRequestException;
import com.grocease.exception.ResourceNotFoundException;
import com.grocease.repository.*;
import com.grocease.util.DtoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class DiscountCodeService {

    private final DiscountCodeRepository discountCodeRepository;
    private final DiscountCodeUsageRepository discountCodeUsageRepository;
    private final UserRepository userRepository;
    private final OrderRepository orderRepository;
    private final DtoMapper dtoMapper;
    private final ObjectMapper objectMapper;

    @Transactional
    public DiscountCodeDto createDiscountCode(CreateDiscountCodeRequest request) {
        log.info("Creating discount code: {}", request.getCode());

        // Check if code already exists
        if (discountCodeRepository.existsByCodeIgnoreCase(request.getCode())) {
            throw new BadRequestException("Discount code already exists: " + request.getCode());
        }

        // Convert category and product lists to JSON
        String applicableCategories = null;
        String applicableProducts = null;

        try {
            if (request.getApplicableCategories() != null && !request.getApplicableCategories().isEmpty()) {
                applicableCategories = objectMapper.writeValueAsString(request.getApplicableCategories());
            }
            if (request.getApplicableProducts() != null && !request.getApplicableProducts().isEmpty()) {
                applicableProducts = objectMapper.writeValueAsString(request.getApplicableProducts());
            }
        } catch (JsonProcessingException e) {
            throw new BadRequestException("Error processing applicable categories/products");
        }

        DiscountCode discountCode = DiscountCode.builder()
                .code(request.getCode().toUpperCase())
                .name(request.getName())
                .description(request.getDescription())
                .type(request.getType())
                .value(request.getValue())
                .minimumOrderAmount(request.getMinimumOrderAmount())
                .maximumDiscountAmount(request.getMaximumDiscountAmount())
                .usageLimit(request.getUsageLimit())
                .usageLimitPerUser(request.getUsageLimitPerUser())
                .validFrom(request.getValidFrom())
                .validUntil(request.getValidUntil())
                .isActive(request.getIsActive())
                .isFirstOrderOnly(request.getIsFirstOrderOnly())
                .applicableCategories(applicableCategories)
                .applicableProducts(applicableProducts)
                .build();

        DiscountCode savedCode = discountCodeRepository.save(discountCode);
        log.info("Discount code created successfully: {}", savedCode.getCode());

        return dtoMapper.toDiscountCodeDto(savedCode);
    }

    public DiscountCalculationResult applyDiscountCode(Long userId, ApplyDiscountRequest request) {
        log.info("Applying discount code: {} for user: {}", request.getCode(), userId);

        try {
            // Find discount code
            DiscountCode discountCode = discountCodeRepository.findByCodeIgnoreCase(request.getCode())
                    .orElseThrow(() -> new BadRequestException("Invalid discount code"));

            // Validate discount code
            String validationError = validateDiscountCode(discountCode, userId, request);
            if (validationError != null) {
                return DiscountCalculationResult.builder()
                        .isValid(false)
                        .message(validationError)
                        .discountAmount(BigDecimal.ZERO)
                        .originalSubtotal(request.getOrderSubtotal())
                        .originalDeliveryFee(request.getDeliveryFee())
                        .finalSubtotal(request.getOrderSubtotal())
                        .finalDeliveryFee(request.getDeliveryFee())
                        .finalTotal(request.getOrderSubtotal().add(request.getDeliveryFee()))
                        .discountCode(request.getCode())
                        .build();
            }

            // Calculate discount
            BigDecimal discountAmount = calculateDiscountAmount(discountCode, request);
            BigDecimal finalDeliveryFee = request.getDeliveryFee();

            // Handle free delivery
            if (discountCode.getType() == DiscountCode.DiscountType.FREE_DELIVERY) {
                finalDeliveryFee = BigDecimal.ZERO;
                discountAmount = request.getDeliveryFee();
            }

            BigDecimal finalTotal = request.getOrderSubtotal().add(finalDeliveryFee).subtract(discountAmount);

            return DiscountCalculationResult.builder()
                    .isValid(true)
                    .message("Discount applied successfully")
                    .discountAmount(discountAmount)
                    .originalSubtotal(request.getOrderSubtotal())
                    .originalDeliveryFee(request.getDeliveryFee())
                    .finalSubtotal(request.getOrderSubtotal())
                    .finalDeliveryFee(finalDeliveryFee)
                    .finalTotal(finalTotal)
                    .discountCode(discountCode.getCode())
                    .discountDescription(discountCode.getDescription())
                    .build();

        } catch (Exception e) {
            log.error("Error applying discount code: {}", request.getCode(), e);
            return DiscountCalculationResult.builder()
                    .isValid(false)
                    .message("Error applying discount code: " + e.getMessage())
                    .discountAmount(BigDecimal.ZERO)
                    .originalSubtotal(request.getOrderSubtotal())
                    .originalDeliveryFee(request.getDeliveryFee())
                    .finalSubtotal(request.getOrderSubtotal())
                    .finalDeliveryFee(request.getDeliveryFee())
                    .finalTotal(request.getOrderSubtotal().add(request.getDeliveryFee()))
                    .discountCode(request.getCode())
                    .build();
        }
    }

    private String validateDiscountCode(DiscountCode discountCode, Long userId, ApplyDiscountRequest request) {
        // Check if code is active and valid
        if (!discountCode.isValid()) {
            if (!discountCode.getIsActive()) {
                return "This discount code is no longer active";
            }
            if (discountCode.isExpired()) {
                return "This discount code has expired";
            }
            if (discountCode.hasReachedUsageLimit()) {
                return "This discount code has reached its usage limit";
            }
        }

        // Check minimum order amount
        if (discountCode.getMinimumOrderAmount() != null && 
            request.getOrderSubtotal().compareTo(discountCode.getMinimumOrderAmount()) < 0) {
            return String.format("Minimum order amount of $%.2f required for this discount", 
                    discountCode.getMinimumOrderAmount());
        }

        // Check user-specific usage limit
        if (discountCode.getUsageLimitPerUser() != null) {
            Long userUsageCount = discountCodeUsageRepository.countByDiscountCodeIdAndUserId(
                    discountCode.getId(), userId);
            if (userUsageCount >= discountCode.getUsageLimitPerUser()) {
                return "You have already used this discount code the maximum number of times";
            }
        }

        // Check if it's for first order only
        if (discountCode.getIsFirstOrderOnly()) {
            Long userOrderCount = orderRepository.countByUserId(userId);
            if (userOrderCount > 0) {
                return "This discount code is only valid for first-time orders";
            }
        }

        // Check applicable categories and products
        if (request.getItems() != null && !request.getItems().isEmpty()) {
            boolean hasApplicableItems = checkApplicableItems(discountCode, request.getItems());
            if (!hasApplicableItems) {
                return "This discount code is not applicable to the items in your cart";
            }
        }

        return null; // Valid
    }

    private boolean checkApplicableItems(DiscountCode discountCode, List<ApplyDiscountRequest.CartItemDto> items) {
        try {
            // If no restrictions, applicable to all items
            if (discountCode.getApplicableCategories() == null && discountCode.getApplicableProducts() == null) {
                return true;
            }

            List<Long> applicableCategories = new ArrayList<>();
            List<Long> applicableProducts = new ArrayList<>();

            if (discountCode.getApplicableCategories() != null) {
                applicableCategories = objectMapper.readValue(
                        discountCode.getApplicableCategories(), new TypeReference<List<Long>>() {});
            }

            if (discountCode.getApplicableProducts() != null) {
                applicableProducts = objectMapper.readValue(
                        discountCode.getApplicableProducts(), new TypeReference<List<Long>>() {});
            }

            // Check if any item matches the criteria
            for (ApplyDiscountRequest.CartItemDto item : items) {
                if (applicableProducts.contains(item.getProductId()) || 
                    applicableCategories.contains(item.getCategoryId())) {
                    return true;
                }
            }

            return false;
        } catch (JsonProcessingException e) {
            log.error("Error parsing applicable categories/products", e);
            return true; // Default to allowing if parsing fails
        }
    }

    private BigDecimal calculateDiscountAmount(DiscountCode discountCode, ApplyDiscountRequest request) {
        BigDecimal discountAmount = BigDecimal.ZERO;

        switch (discountCode.getType()) {
            case PERCENTAGE:
                discountAmount = request.getOrderSubtotal()
                        .multiply(discountCode.getValue())
                        .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
                break;
            case FIXED_AMOUNT:
                discountAmount = discountCode.getValue();
                break;
            case FREE_DELIVERY:
                discountAmount = request.getDeliveryFee();
                break;
        }

        // Apply maximum discount limit
        if (discountCode.getMaximumDiscountAmount() != null && 
            discountAmount.compareTo(discountCode.getMaximumDiscountAmount()) > 0) {
            discountAmount = discountCode.getMaximumDiscountAmount();
        }

        // Ensure discount doesn't exceed order subtotal (except for free delivery)
        if (discountCode.getType() != DiscountCode.DiscountType.FREE_DELIVERY && 
            discountAmount.compareTo(request.getOrderSubtotal()) > 0) {
            discountAmount = request.getOrderSubtotal();
        }

        return discountAmount;
    }

    @Transactional
    public void recordDiscountUsage(String discountCode, Long userId, Long orderId, BigDecimal discountAmount, BigDecimal orderSubtotal) {
        log.info("Recording discount usage: {} for user: {} order: {}", discountCode, userId, orderId);

        DiscountCode code = discountCodeRepository.findByCodeIgnoreCase(discountCode)
                .orElseThrow(() -> new ResourceNotFoundException("Discount code not found: " + discountCode));

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + userId));

        Order order = orderRepository.findById(orderId)
                .orElseThrow(() -> new ResourceNotFoundException("Order not found: " + orderId));

        // Create usage record
        DiscountCodeUsage usage = DiscountCodeUsage.builder()
                .discountCode(code)
                .user(user)
                .order(order)
                .discountAmount(discountAmount)
                .orderSubtotal(orderSubtotal)
                .build();

        discountCodeUsageRepository.save(usage);

        // Increment usage count
        code.setUsedCount(code.getUsedCount() + 1);
        discountCodeRepository.save(code);

        log.info("Discount usage recorded successfully");
    }

    public PaginatedResponse<DiscountCodeDto> getAllDiscountCodes(int page, int limit, Boolean activeOnly) {
        // Validate and sanitize pagination parameters to prevent IndexOutOfBoundsException
        page = Math.max(0, page); // Ensure page is not negative
        limit = Math.max(1, Math.min(100, limit)); // Ensure limit is between 1 and 100

        log.info("Getting discount codes with validated params - page: {}, limit: {}, activeOnly: {}", page, limit, activeOnly);

        Pageable pageable = PageRequest.of(page, limit);
        Page<DiscountCode> discountCodePage;

        if (activeOnly != null && activeOnly) {
            discountCodePage = discountCodeRepository.findByIsActiveTrueOrderByCreatedAtDesc(pageable);
        } else {
            discountCodePage = discountCodeRepository.findAllByOrderByCreatedAtDesc(pageable);
        }

        List<DiscountCodeDto> discountCodeDtos = discountCodePage.getContent().stream()
                .map(dtoMapper::toDiscountCodeDto)
                .collect(Collectors.toList());

        return PaginatedResponse.<DiscountCodeDto>builder()
                .data(discountCodeDtos)
                .pagination(PaginatedResponse.PaginationInfo.builder()
                        .page(page)
                        .limit(limit)
                        .total(discountCodePage.getTotalElements())
                        .totalPages(discountCodePage.getTotalPages())
                        .build())
                .build();
    }

    public DiscountCodeDto getDiscountCodeById(Long id) {
        log.info("Getting discount code by id: {}", id);
        DiscountCode discountCode = discountCodeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Discount code not found with id: " + id));
        return dtoMapper.toDiscountCodeDto(discountCode);
    }

    @Transactional
    public DiscountCodeDto updateDiscountCode(Long id, CreateDiscountCodeRequest request) {
        log.info("Updating discount code: {}", id);

        DiscountCode discountCode = discountCodeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Discount code not found with id: " + id));

        // Check if code already exists (excluding current code)
        if (!discountCode.getCode().equalsIgnoreCase(request.getCode()) && 
            discountCodeRepository.existsByCodeIgnoreCase(request.getCode())) {
            throw new BadRequestException("Discount code already exists: " + request.getCode());
        }

        // Convert category and product lists to JSON
        String applicableCategories = null;
        String applicableProducts = null;

        try {
            if (request.getApplicableCategories() != null && !request.getApplicableCategories().isEmpty()) {
                applicableCategories = objectMapper.writeValueAsString(request.getApplicableCategories());
            }
            if (request.getApplicableProducts() != null && !request.getApplicableProducts().isEmpty()) {
                applicableProducts = objectMapper.writeValueAsString(request.getApplicableProducts());
            }
        } catch (JsonProcessingException e) {
            throw new BadRequestException("Error processing applicable categories/products");
        }

        // Update fields
        discountCode.setCode(request.getCode().toUpperCase());
        discountCode.setName(request.getName());
        discountCode.setDescription(request.getDescription());
        discountCode.setType(request.getType());
        discountCode.setValue(request.getValue());
        discountCode.setMinimumOrderAmount(request.getMinimumOrderAmount());
        discountCode.setMaximumDiscountAmount(request.getMaximumDiscountAmount());
        discountCode.setUsageLimit(request.getUsageLimit());
        discountCode.setUsageLimitPerUser(request.getUsageLimitPerUser());
        discountCode.setValidFrom(request.getValidFrom());
        discountCode.setValidUntil(request.getValidUntil());
        discountCode.setIsActive(request.getIsActive());
        discountCode.setIsFirstOrderOnly(request.getIsFirstOrderOnly());
        discountCode.setApplicableCategories(applicableCategories);
        discountCode.setApplicableProducts(applicableProducts);

        DiscountCode savedCode = discountCodeRepository.save(discountCode);
        log.info("Discount code updated successfully: {}", savedCode.getCode());

        return dtoMapper.toDiscountCodeDto(savedCode);
    }

    @Transactional
    public void deleteDiscountCode(Long id) {
        log.info("Deleting discount code: {}", id);
        DiscountCode discountCode = discountCodeRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Discount code not found with id: " + id));

        // Check if code has been used
        if (discountCode.getUsedCount() > 0) {
            // Soft delete - just deactivate
            discountCode.setIsActive(false);
            discountCodeRepository.save(discountCode);
            log.info("Discount code deactivated (has usage history): {}", discountCode.getCode());
        } else {
            // Hard delete if never used
            discountCodeRepository.delete(discountCode);
            log.info("Discount code deleted: {}", discountCode.getCode());
        }
    }

    public List<DiscountCodeDto> getAvailableDiscountCodes() {
        log.info("Getting available discount codes");
        List<DiscountCode> codes = discountCodeRepository.findAvailableDiscountCodes(LocalDateTime.now());
        return codes.stream()
                .map(dtoMapper::toDiscountCodeDto)
                .collect(Collectors.toList());
    }

    public DiscountCodeDto getDiscountCodeByCode(String code) {
        log.info("Getting discount code by code: {}", code);
        DiscountCode discountCode = discountCodeRepository.findByCodeIgnoreCase(code)
                .orElseThrow(() -> new ResourceNotFoundException("Discount code not found: " + code));
        return dtoMapper.toDiscountCodeDto(discountCode);
    }
}
