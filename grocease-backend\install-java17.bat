@echo off
echo Installing Java 17 for GrocEase Backend...

REM Check if Java 17 is already available
echo Checking for Java 17...
if exist "C:\Program Files\Eclipse Adoptium\jdk-17*" (
    echo Java 17 found in Eclipse Adoptium directory
    goto :setpath
)

if exist "C:\Program Files\Java\jdk-17*" (
    echo Java 17 found in Oracle Java directory
    goto :setpath
)

echo Java 17 not found. Downloading...

REM Create tools directory if it doesn't exist
if not exist "C:\tools" mkdir "C:\tools"

REM Download Java 17
echo Downloading Eclipse Temurin JDK 17...
powershell -Command "& {Invoke-WebRequest -Uri 'https://github.com/adoptium/temurin17-binaries/releases/download/jdk-17.0.9%2B9/OpenJDK17U-jdk_x64_windows_hotspot_17.0.9_9.zip' -OutFile 'C:\tools\java17.zip'}"

if not exist "C:\tools\java17.zip" (
    echo ERROR: Failed to download Java 17!
    echo Please download Java 17 manually from: https://adoptium.net/
    pause
    exit /b 1
)

REM Extract Java 17
echo Extracting Java 17...
powershell -Command "& {Expand-Archive -Path 'C:\tools\java17.zip' -DestinationPath 'C:\tools' -Force}"

REM Find the extracted directory
for /d %%i in ("C:\tools\jdk-17*") do set JAVA17_HOME=%%i

if not defined JAVA17_HOME (
    echo ERROR: Could not find extracted Java 17 directory!
    pause
    exit /b 1
)

echo Java 17 extracted to: %JAVA17_HOME%

REM Clean up
del "C:\tools\java17.zip"

:setpath
REM Find Java 17 installation
for /d %%i in ("C:\Program Files\Eclipse Adoptium\jdk-17*") do set JAVA17_HOME=%%i
if not defined JAVA17_HOME (
    for /d %%i in ("C:\Program Files\Java\jdk-17*") do set JAVA17_HOME=%%i
)
if not defined JAVA17_HOME (
    for /d %%i in ("C:\tools\jdk-17*") do set JAVA17_HOME=%%i
)

if not defined JAVA17_HOME (
    echo ERROR: Could not locate Java 17 installation!
    pause
    exit /b 1
)

echo Setting JAVA_HOME to: %JAVA17_HOME%

REM Set JAVA_HOME for current session
set JAVA_HOME=%JAVA17_HOME%
set PATH=%JAVA17_HOME%\bin;%PATH%

REM Set JAVA_HOME permanently
setx JAVA_HOME "%JAVA17_HOME%" /M
setx PATH "%JAVA17_HOME%\bin;%PATH%" /M

echo.
echo ✅ Java 17 installation completed!
echo.
echo JAVA_HOME: %JAVA_HOME%
echo.
echo Testing Java 17...
"%JAVA17_HOME%\bin\java" -version

echo.
echo Please RESTART your command prompt and then run:
echo   java -version
echo   mvn clean compile
echo.

pause
