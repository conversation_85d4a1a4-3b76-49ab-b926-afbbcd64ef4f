                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            2025-07-11 11:48:52.502 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 17.0.9 with PID 7056 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 11:48:52.505 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 11:48:54.078 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 11:48:54.251 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 164 ms. Found 13 JPA repository interfaces.
2025-07-11 11:48:56.021 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - <PERSON><PERSON> initialized with port 8080 (http)
2025-07-11 11:48:56.033 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 11:48:56.033 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.24]
2025-07-11 11:48:56.177 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-11 11:48:56.178 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3619 ms
2025-07-11 11:48:56.622 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 11:48:56.718 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.5.2.Final
2025-07-11 11:48:56.764 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-11 11:48:57.203 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11 11:48:57.255 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 11:48:58.270 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@77fb1002
2025-07-11 11:48:58.272 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 11:49:00.110 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11 11:49:00.508 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - SQL Warning Code: 0, SQLState: 00000
2025-07-11 11:49:00.508 [main] WARN  o.h.e.jdbc.spi.SqlExceptionHelper - constraint "ukekkk9piidfon0rluedtqr82uv" of relation "discount_codes" does not exist, skipping
2025-07-11 11:49:00.777 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 11:49:01.184 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-11 11:49:02.182 [main] WARN  c.g.service.NotificationService - Firebase messaging is not available. Push notifications will be disabled.
2025-07-11 11:49:02.821 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used for username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider.
2025-07-11 11:49:02.886 [main] INFO  c.grocease.config.CloudinaryConfig - Initializing Cloudinary with cloud name: groceease
2025-07-11 11:49:03.033 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-11 11:49:03.518 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-11 11:49:04.209 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-07-11 11:49:04.229 [main] INFO  com.grocease.GrocEaseApplication - Started GrocEaseApplication in 12.574 seconds (process running for 13.286)
2025-07-11 11:51:23.564 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 11:51:23.566 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-11 11:51:23.569 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-11 11:51:31.561 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 17.0.9 with PID 13184 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 11:51:31.563 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 11:51:32.347 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 11:51:32.429 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 75 ms. Found 13 JPA repository interfaces.
2025-07-11 11:51:33.141 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-11 11:51:33.150 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 11:51:33.150 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.24]
2025-07-11 11:51:33.212 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-11 11:51:33.212 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1606 ms
2025-07-11 11:51:33.475 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 11:51:33.536 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.5.2.Final
2025-07-11 11:51:33.564 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-11 11:51:33.795 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11 11:51:33.817 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 11:51:33.954 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6942ee48
2025-07-11 11:51:33.956 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 11:51:35.063 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11 11:51:35.277 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 11:51:35.552 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-11 11:51:36.297 [main] WARN  c.g.service.NotificationService - Firebase messaging is not available. Push notifications will be disabled.
2025-07-11 11:51:36.765 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used for username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider.
2025-07-11 11:51:36.796 [main] INFO  c.grocease.config.CloudinaryConfig - Initializing Cloudinary with cloud name: groceease
2025-07-11 11:51:36.863 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-11 11:51:37.171 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-11 11:51:37.694 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-07-11 11:51:37.710 [main] INFO  com.grocease.GrocEaseApplication - Started GrocEaseApplication in 6.571 seconds (process running for 6.901)
2025-07-11 11:52:34.081 [http-nio-8080-exec-9] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 11:57:58.443 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 11:57:58.445 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-11 11:57:58.449 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-11 12:11:00.430 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 24.0.1 with PID 16952 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 12:11:00.436 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 12:11:01.409 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 12:11:01.499 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 79 ms. Found 13 JPA repository interfaces.
2025-07-11 12:11:02.332 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-11 12:11:02.343 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 12:11:02.344 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.24]
2025-07-11 12:11:02.410 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-11 12:11:02.411 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1899 ms
2025-07-11 12:11:02.711 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 12:11:02.811 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.5.2.Final
2025-07-11 12:11:02.843 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-11 12:11:03.118 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11 12:11:03.146 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 12:11:03.315 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@416d90f0
2025-07-11 12:11:03.317 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 12:11:04.588 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11 12:11:04.844 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 12:11:05.158 [main] INFO  o.s.d.j.r.query.QueryEnhancerFactory - Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-11 12:11:06.048 [main] WARN  c.g.service.NotificationService - Firebase messaging is not available. Push notifications will be disabled.
2025-07-11 12:11:06.694 [main] WARN  o.s.s.c.a.a.c.InitializeUserDetailsBeanManagerConfigurer$InitializeUserDetailsManagerConfigurer - Global AuthenticationManager configured with an AuthenticationProvider bean. UserDetailsService beans will not be used for username/password login. Consider removing the AuthenticationProvider bean. Alternatively, consider using the UserDetailsService in a manually instantiated DaoAuthenticationProvider.
2025-07-11 12:11:06.757 [main] INFO  c.grocease.config.CloudinaryConfig - Initializing Cloudinary with cloud name: groceease
2025-07-11 12:11:06.861 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-11 12:11:07.249 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-11 12:11:07.907 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-07-11 12:11:07.922 [main] INFO  com.grocease.GrocEaseApplication - Started GrocEaseApplication in 8.002 seconds (process running for 8.393)
2025-07-11 12:12:20.476 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-11 12:59:37.923 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 12:59:37.941 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-11 12:59:37.952 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-11 13:38:13.234 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 24.0.1 with PID 4636 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 13:38:13.237 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 13:38:13.433 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
2025-07-11 13:38:13.719 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:504)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.findCandidateComponents(ClassPathScanningCandidateComponentProvider.java:351)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:277)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:306)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:246)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:197)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.grocease.GrocEaseApplication.main(GrocEaseApplication.java:16)
Caused by: org.springframework.core.type.classreading.ClassFormatException: ASM ClassReader failed to parse class file - probably due to a new Java class file version that is not supported yet. Consider compiling with a lower '-target' or upgrade your framework version. Affected class: file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:59)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:48)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.core.type.classreading.CachingMetadataReaderFactory.getMetadataReader(CachingMetadataReaderFactory.java:122)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:470)
	... 20 common frames omitted
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:199)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:180)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:166)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:287)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:56)
	... 24 common frames omitted
2025-07-11 13:42:54.818 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 24.0.1 with PID 23328 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 13:42:54.822 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 13:42:54.992 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
2025-07-11 13:42:55.245 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:504)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.findCandidateComponents(ClassPathScanningCandidateComponentProvider.java:351)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:277)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:306)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:246)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:197)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.grocease.GrocEaseApplication.main(GrocEaseApplication.java:16)
Caused by: org.springframework.core.type.classreading.ClassFormatException: ASM ClassReader failed to parse class file - probably due to a new Java class file version that is not supported yet. Consider compiling with a lower '-target' or upgrade your framework version. Affected class: file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:59)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:48)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.core.type.classreading.CachingMetadataReaderFactory.getMetadataReader(CachingMetadataReaderFactory.java:122)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:470)
	... 20 common frames omitted
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:199)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:180)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:166)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:287)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:56)
	... 24 common frames omitted
2025-07-11 13:43:18.323 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 24.0.1 with PID 20884 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 13:43:18.325 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 13:43:19.831 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-11 13:43:19.893 [main] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 53 ms. Found 0 JPA repository interfaces.
2025-07-11 13:43:21.135 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-07-11 13:43:21.164 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-07-11 13:43:21.165 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.33]
2025-07-11 13:43:21.311 [main] INFO  o.a.c.c.C.[.[localhost].[/api] - Initializing Spring embedded WebApplicationContext
2025-07-11 13:43:21.312 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 2906 ms
2025-07-11 13:43:21.902 [main] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-11 13:43:21.995 [main] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.5.3.Final
2025-07-11 13:43:22.059 [main] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-07-11 13:43:22.459 [main] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-11 13:43:22.509 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-11 13:43:22.788 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@59c862af
2025-07-11 13:43:22.792 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-11 13:43:23.372 [main] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-11 13:43:23.378 [main] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 13:43:25.081 [main] WARN  o.s.b.a.s.s.UserDetailsServiceAutoConfiguration - 

Using generated security password: ad374402-3061-482c-a96e-8d1daeb8140a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-07-11 13:43:25.326 [main] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-07-11 13:43:25.707 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/api'
2025-07-11 13:43:25.723 [main] INFO  com.grocease.GrocEaseApplication - Started GrocEaseApplication in 8.003 seconds (process running for 8.543)
2025-07-11 13:44:46.899 [SpringApplicationShutdownHook] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-07-11 13:44:46.904 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-11 13:44:46.910 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-11 13:45:21.986 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 24.0.1 with PID 21700 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 13:45:21.989 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 13:45:22.174 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
2025-07-11 13:45:22.586 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:504)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.findCandidateComponents(ClassPathScanningCandidateComponentProvider.java:351)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:277)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:306)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:246)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:197)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.grocease.GrocEaseApplication.main(GrocEaseApplication.java:16)
Caused by: org.springframework.core.type.classreading.ClassFormatException: ASM ClassReader failed to parse class file - probably due to a new Java class file version that is not supported yet. Consider compiling with a lower '-target' or upgrade your framework version. Affected class: file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:59)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:48)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.core.type.classreading.CachingMetadataReaderFactory.getMetadataReader(CachingMetadataReaderFactory.java:122)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:470)
	... 20 common frames omitted
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:199)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:180)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:166)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:287)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:56)
	... 24 common frames omitted
2025-07-11 13:45:23.765 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 24.0.1 with PID 17616 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 13:45:23.769 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 13:45:23.939 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
2025-07-11 13:45:24.184 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:504)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.findCandidateComponents(ClassPathScanningCandidateComponentProvider.java:351)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:277)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:306)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:246)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:197)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.grocease.GrocEaseApplication.main(GrocEaseApplication.java:16)
Caused by: org.springframework.core.type.classreading.ClassFormatException: ASM ClassReader failed to parse class file - probably due to a new Java class file version that is not supported yet. Consider compiling with a lower '-target' or upgrade your framework version. Affected class: file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:59)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:48)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.core.type.classreading.CachingMetadataReaderFactory.getMetadataReader(CachingMetadataReaderFactory.java:122)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:470)
	... 20 common frames omitted
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:199)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:180)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:166)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:287)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:56)
	... 24 common frames omitted
2025-07-11 13:45:35.402 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 24.0.1 with PID 336 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 13:45:35.404 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 13:45:35.587 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
2025-07-11 13:45:35.842 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:504)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.findCandidateComponents(ClassPathScanningCandidateComponentProvider.java:351)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:277)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:306)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:246)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:197)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.grocease.GrocEaseApplication.main(GrocEaseApplication.java:16)
Caused by: org.springframework.core.type.classreading.ClassFormatException: ASM ClassReader failed to parse class file - probably due to a new Java class file version that is not supported yet. Consider compiling with a lower '-target' or upgrade your framework version. Affected class: file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:59)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:48)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.core.type.classreading.CachingMetadataReaderFactory.getMetadataReader(CachingMetadataReaderFactory.java:122)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:470)
	... 20 common frames omitted
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:199)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:180)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:166)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:287)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:56)
	... 24 common frames omitted
2025-07-11 13:52:02.435 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 24.0.1 with PID 22288 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 13:52:02.439 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 13:52:02.597 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
2025-07-11 13:52:02.848 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:504)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.findCandidateComponents(ClassPathScanningCandidateComponentProvider.java:351)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:277)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:306)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:246)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:197)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.grocease.GrocEaseApplication.main(GrocEaseApplication.java:16)
Caused by: org.springframework.core.type.classreading.ClassFormatException: ASM ClassReader failed to parse class file - probably due to a new Java class file version that is not supported yet. Consider compiling with a lower '-target' or upgrade your framework version. Affected class: file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\config\AppProperties$Cors.class]
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:59)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:48)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.core.type.classreading.CachingMetadataReaderFactory.getMetadataReader(CachingMetadataReaderFactory.java:122)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:470)
	... 20 common frames omitted
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:199)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:180)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:166)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:287)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:56)
	... 24 common frames omitted
2025-07-11 13:57:10.543 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 24.0.1 with PID 18876 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 13:57:10.546 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 13:57:10.842 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\service\DiscountCodeService$1.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
2025-07-11 13:57:11.044 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\service\DiscountCodeService$1.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:504)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.findCandidateComponents(ClassPathScanningCandidateComponentProvider.java:351)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:277)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:306)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:246)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:197)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.grocease.GrocEaseApplication.main(GrocEaseApplication.java:16)
Caused by: org.springframework.core.type.classreading.ClassFormatException: ASM ClassReader failed to parse class file - probably due to a new Java class file version that is not supported yet. Consider compiling with a lower '-target' or upgrade your framework version. Affected class: file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\service\DiscountCodeService$1.class]
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:59)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:48)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.core.type.classreading.CachingMetadataReaderFactory.getMetadataReader(CachingMetadataReaderFactory.java:122)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:470)
	... 20 common frames omitted
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:199)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:180)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:166)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:287)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:56)
	... 24 common frames omitted
2025-07-11 13:57:48.374 [main] INFO  com.grocease.GrocEaseApplication - Starting GrocEaseApplication using Java 24.0.1 with PID 20760 (C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes started by Admin in C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend)
2025-07-11 13:57:48.376 [main] INFO  com.grocease.GrocEaseApplication - No active profile set, falling back to 1 default profile: "default"
2025-07-11 13:57:48.630 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\service\DiscountCodeService$1.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
2025-07-11 13:57:48.843 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanDefinitionStoreException: Incompatible class format in file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\service\DiscountCodeService$1.class]: set system property 'spring.classformat.ignore' to 'true' if you mean to ignore such files during classpath scanning
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:504)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.findCandidateComponents(ClassPathScanningCandidateComponentProvider.java:351)
	at org.springframework.context.annotation.ClassPathBeanDefinitionScanner.doScan(ClassPathBeanDefinitionScanner.java:277)
	at org.springframework.context.annotation.ComponentScanAnnotationParser.parse(ComponentScanAnnotationParser.java:128)
	at org.springframework.context.annotation.ConfigurationClassParser.doProcessConfigurationClass(ConfigurationClassParser.java:306)
	at org.springframework.context.annotation.ConfigurationClassParser.processConfigurationClass(ConfigurationClassParser.java:246)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:197)
	at org.springframework.context.annotation.ConfigurationClassParser.parse(ConfigurationClassParser.java:165)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.processConfigBeanDefinitions(ConfigurationClassPostProcessor.java:417)
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanDefinitionRegistry(ConfigurationClassPostProcessor.java:290)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanDefinitionRegistryPostProcessors(PostProcessorRegistrationDelegate.java:349)
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:118)
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:789)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:607)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1363)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1352)
	at com.grocease.GrocEaseApplication.main(GrocEaseApplication.java:16)
Caused by: org.springframework.core.type.classreading.ClassFormatException: ASM ClassReader failed to parse class file - probably due to a new Java class file version that is not supported yet. Consider compiling with a lower '-target' or upgrade your framework version. Affected class: file [C:\Users\<USER>\Desktop\ashish\Grocery-app\grocease-backend\target\classes\com\grocease\service\DiscountCodeService$1.class]
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:59)
	at org.springframework.core.type.classreading.SimpleMetadataReader.<init>(SimpleMetadataReader.java:48)
	at org.springframework.core.type.classreading.SimpleMetadataReaderFactory.getMetadataReader(SimpleMetadataReaderFactory.java:103)
	at org.springframework.core.type.classreading.CachingMetadataReaderFactory.getMetadataReader(CachingMetadataReaderFactory.java:122)
	at org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider.scanCandidateComponents(ClassPathScanningCandidateComponentProvider.java:470)
	... 20 common frames omitted
Caused by: java.lang.IllegalArgumentException: Unsupported class file major version 68
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:199)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:180)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:166)
	at org.springframework.asm.ClassReader.<init>(ClassReader.java:287)
	at org.springframework.core.type.classreading.SimpleMetadataReader.getClassReader(SimpleMetadataReader.java:56)
	... 24 common frames omitted
