import React, { useState } from 'react';
import { View, Text, TouchableOpacity, ScrollView, Alert } from 'react-native';
import { runApiTests, TestSuite, TestResult } from '../utils/apiTest';
import { api } from '../services/api';
import { authApi } from '../services/authApi';
import { httpClient } from '../services/httpClient';

interface ApiTestComponentProps {
  onClose?: () => void;
}

export const ApiTestComponent: React.FC<ApiTestComponentProps> = ({ onClose }) => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestSuite | null>(null);

  const handleRunTests = async () => {
    setIsRunning(true);
    setTestResults(null);

    try {
      const results = await runApiTests();
      setTestResults(results);
    } catch (error) {
      Alert.alert('Test Error', error instanceof Error ? error.message : 'Failed to run tests');
    } finally {
      setIsRunning(false);
    }
  };

  const handleTestIndividualAPI = async (apiName: string) => {
    setIsRunning(true);
    
    try {
      let result;
      switch (apiName) {
        case 'categories':
          result = await api.getCategories();
          break;
        case 'products':
          result = await api.getProducts();
          break;
        case 'banners':
          result = await api.getBanners();
          break;
        case 'login':
          result = await authApi.login({
            email: '<EMAIL>',
            password: 'password123'
          });
          break;
        default:
          throw new Error('Unknown API');
      }

      Alert.alert(
        `${apiName} API Test`,
        result.success ? 'Success!' : `Failed: ${result.message}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert(
        'API Test Error',
        error instanceof Error ? error.message : 'Test failed'
      );
    } finally {
      setIsRunning(false);
    }
  };

  const handleTestNetworkLogging = async () => {
    setIsRunning(true);
    try {
      console.log('🧪 Testing Network Logging...');

      // Clear existing logs
      httpClient.clearNetworkLogs();

      // Make a few test requests to generate logs
      await authApi.login({ email: '<EMAIL>', password: 'wrongpassword' }).catch(() => {});
      await api.getProducts({ page: 0, limit: 5 }).catch(() => {});
      await api.getCategories().catch(() => {});

      // Get the logs
      const logs = httpClient.getNetworkLogs();
      console.log('📊 Network Logs Generated:', logs.length);

      Alert.alert(
        'Network Logging Test',
        `Generated ${logs.length} network logs. Check the console and the debug panel!`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Network Test Error', error instanceof Error ? error.message : 'Test failed');
    } finally {
      setIsRunning(false);
    }
  };

  const renderTestResult = (result: TestResult) => {
    const bgColor = result.success ? '#dcfce7' : '#fef2f2';
    const textColor = result.success ? '#166534' : '#dc2626';
    const icon = result.success ? '✅' : '❌';

    return (
      <View
        key={result.name}
        className="p-3 mb-2 rounded-lg border"
        style={{ backgroundColor: bgColor, borderColor: textColor }}
      >
        <Text className="font-semibold" style={{ color: textColor }}>
          {icon} {result.name}
        </Text>
        <Text className="text-sm mt-1" style={{ color: textColor }}>
          {result.message} ({result.duration}ms)
        </Text>
        {result.error && (
          <Text className="text-xs mt-1 opacity-75" style={{ color: textColor }}>
            {result.error.toString()}
          </Text>
        )}
      </View>
    );
  };

  return (
    <View className="flex-1 bg-white p-4">
      <View className="flex-row justify-between items-center mb-6">
        <Text className="text-2xl font-bold text-gray-900">API Integration Test</Text>
        {onClose && (
          <TouchableOpacity
            onPress={onClose}
            className="bg-gray-200 px-4 py-2 rounded-lg"
          >
            <Text className="text-gray-700">Close</Text>
          </TouchableOpacity>
        )}
      </View>

      <ScrollView className="flex-1">
        {/* Run All Tests Button */}
        <TouchableOpacity
          onPress={handleRunTests}
          disabled={isRunning}
          className={`p-4 rounded-lg mb-4 ${
            isRunning ? 'bg-gray-300' : 'bg-blue-500'
          }`}
        >
          <Text className="text-white text-center font-semibold">
            {isRunning ? 'Running Tests...' : 'Run All API Tests'}
          </Text>
        </TouchableOpacity>

        {/* Individual API Test Buttons */}
        <Text className="text-lg font-semibold mb-3 text-gray-900">
          Test Individual APIs:
        </Text>
        
        <View className="flex-row flex-wrap gap-2 mb-6">
          {['categories', 'products', 'banners', 'login'].map((apiName) => (
            <TouchableOpacity
              key={apiName}
              onPress={() => handleTestIndividualAPI(apiName)}
              disabled={isRunning}
              className={`px-4 py-2 rounded-lg ${
                isRunning ? 'bg-gray-300' : 'bg-green-500'
              }`}
            >
              <Text className="text-white font-medium capitalize">
                {apiName}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Network Logging Test Button */}
        <TouchableOpacity
          onPress={handleTestNetworkLogging}
          disabled={isRunning}
          className={`p-4 rounded-lg mb-4 ${
            isRunning ? 'bg-gray-300' : 'bg-purple-500'
          }`}
        >
          <Text className="text-white text-center font-semibold">
            {isRunning ? 'Testing...' : 'Test Network Logging'}
          </Text>
        </TouchableOpacity>

        {/* Test Results */}
        {testResults && (
          <View className="mb-6">
            <Text className="text-lg font-semibold mb-3 text-gray-900">
              Test Results:
            </Text>
            
            <View className="bg-gray-100 p-4 rounded-lg mb-4">
              <Text className="font-semibold text-gray-900">
                Summary: {testResults.passedTests}/{testResults.totalTests} tests passed
              </Text>
              <Text className="text-sm text-gray-600">
                Total Duration: {testResults.totalDuration}ms
              </Text>
            </View>

            {testResults.results.map(renderTestResult)}
          </View>
        )}

        {/* Instructions */}
        <View className="bg-blue-50 p-4 rounded-lg border border-blue-200">
          <Text className="font-semibold text-blue-900 mb-2">
            Testing Instructions:
          </Text>
          <Text className="text-sm text-blue-800 mb-1">
            1. Make sure the backend server is running on localhost:8080
          </Text>
          <Text className="text-sm text-blue-800 mb-1">
            2. Run "Run All API Tests" to test complete integration
          </Text>
          <Text className="text-sm text-blue-800 mb-1">
            3. Use individual API buttons to test specific endpoints
          </Text>
          <Text className="text-sm text-blue-800">
            4. Check console logs for detailed error information
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};
