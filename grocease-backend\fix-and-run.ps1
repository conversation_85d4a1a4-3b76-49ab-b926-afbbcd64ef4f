#!/usr/bin/env pwsh

Write-Host "GrocEase Backend - Complete Fix and Run Script" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Set environment variables
$env:JWT_SECRET = "GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789"
$env:EMAIL_VERIFICATION_ENABLED = "false"
$env:DB_USERNAME = "postgres"
$env:DB_PASSWORD = "admin"
$env:CLOUDINARY_ENABLED = "true"
$env:PATH = "$env:PATH;C:\tools\maven\bin"

Write-Host "Environment variables configured" -ForegroundColor Green

# Check Java version
Write-Host "Checking Java version..." -ForegroundColor Yellow
try {
    $javaVersion = & java -version 2>&1
    $versionLine = $javaVersion[0]
    Write-Host "Current Java: $versionLine" -ForegroundColor Cyan
    
    if ($versionLine -match 'version "(\d+)') {
        $majorVersion = [int]$matches[1]
        if ($majorVersion -gt 17) {
            Write-Host "WARNING: Java $majorVersion detected. This may cause compilation issues." -ForegroundColor Yellow
            Write-Host "The application is configured for Java 17." -ForegroundColor Yellow
            Write-Host "Attempting to run anyway..." -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "ERROR: Java not found: $_" -ForegroundColor Red
    exit 1
}

# Check Maven
Write-Host "Checking Maven..." -ForegroundColor Yellow
try {
    $mvnVersion = & mvn --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Maven is available" -ForegroundColor Green
    } else {
        throw "Maven not found"
    }
} catch {
    Write-Host "ERROR: Maven not found. Please run install-maven.bat first" -ForegroundColor Red
    exit 1
}

# Test configuration
Write-Host "Testing configuration..." -ForegroundColor Yellow

# Test JWT Secret
$jwtSecret = $env:JWT_SECRET
if ($jwtSecret.Length -ge 32) {
    Write-Host "JWT Secret: Valid ($($jwtSecret.Length) characters)" -ForegroundColor Green
} else {
    Write-Host "ERROR: JWT Secret too short" -ForegroundColor Red
    exit 1
}

# Test string operations (potential IndexOutOfBoundsException source)
try {
    $testHeader = "Bearer test-token-12345"
    if ($testHeader.Length -gt 7) {
        $extracted = $testHeader.Substring(7)
        Write-Host "String operations: OK" -ForegroundColor Green
    }
} catch {
    Write-Host "ERROR: String operation test failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "All checks passed! Starting application..." -ForegroundColor Green
Write-Host ""
Write-Host "FIXES APPLIED:" -ForegroundColor Cyan
Write-Host "- Enhanced JWT token validation with bounds checking" -ForegroundColor White
Write-Host "- Improved CloudinaryService error handling" -ForegroundColor White
Write-Host "- Added startup configuration validation" -ForegroundColor White
Write-Host "- Fixed multipart file upload configuration" -ForegroundColor White
Write-Host "- Enhanced IndexOutOfBoundsException handling" -ForegroundColor White
Write-Host ""
Write-Host "Press Ctrl+C to stop the application" -ForegroundColor Yellow
Write-Host ""

# Try to compile first
Write-Host "Compiling application..." -ForegroundColor Yellow
try {
    $compileResult = & mvn clean compile -q 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Compilation failed. Trying with different Java compatibility..." -ForegroundColor Yellow
        
        # Try with Java 11 compatibility
        $env:MAVEN_OPTS = "-Djava.version=11 -Dmaven.compiler.source=11 -Dmaven.compiler.target=11"
        $compileResult = & mvn clean compile -Djava.version=11 -Dmaven.compiler.source=11 -Dmaven.compiler.target=11 -q 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "ERROR: Compilation failed even with Java 11 compatibility" -ForegroundColor Red
            Write-Host "This is likely due to Java version incompatibility." -ForegroundColor Red
            Write-Host ""
            Write-Host "SOLUTION: Install Java 17" -ForegroundColor Yellow
            Write-Host "1. Download from: https://adoptium.net/temurin/releases/?version=17" -ForegroundColor White
            Write-Host "2. Install Java 17" -ForegroundColor White
            Write-Host "3. Set JAVA_HOME to Java 17 installation" -ForegroundColor White
            Write-Host "4. Restart command prompt and try again" -ForegroundColor White
            exit 1
        }
    }
    Write-Host "Compilation successful!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Compilation failed: $_" -ForegroundColor Red
    exit 1
}

# Run the application
Write-Host "Starting Spring Boot application..." -ForegroundColor Green
try {
    & mvn spring-boot:run
} catch {
    Write-Host "Application failed to start: $_" -ForegroundColor Red
    exit 1
}
