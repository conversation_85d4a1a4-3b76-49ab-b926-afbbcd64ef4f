#!/usr/bin/env pwsh

Write-Host "Testing IndexOutOfBoundsException Fixes..." -ForegroundColor Green

# Set environment variables
$env:JWT_SECRET = "GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789"
$env:EMAIL_VERIFICATION_ENABLED = "false"
$env:DB_USERNAME = "postgres"
$env:DB_PASSWORD = "admin"
$env:CLOUDINARY_ENABLED = "true"
$env:PATH = "$env:PATH;C:\tools\maven\bin"

Write-Host "Environment variables set" -ForegroundColor Green

# Test JWT Secret length
$jwtSecret = $env:JWT_SECRET
Write-Host "JWT Secret length: $($jwtSecret.Length) characters" -ForegroundColor Cyan
if ($jwtSecret.Length -ge 32) {
    Write-Host "JWT Secret length is valid" -ForegroundColor Green
} else {
    Write-Host "JWT Secret is too short" -ForegroundColor Red
    exit 1
}

# Test string operations that might cause IndexOutOfBoundsException
Write-Host "Testing string operations..." -ForegroundColor Yellow
try {
    $testHeader = "Bearer test-token-12345"
    if ($testHeader.Length -gt 7) {
        $extracted = $testHeader.Substring(7)
        Write-Host "String substring test passed: '$extracted'" -ForegroundColor Green
    }
} catch {
    Write-Host "String operation test failed: $_" -ForegroundColor Red
    exit 1
}

# Test Maven availability
Write-Host "Testing Maven..." -ForegroundColor Yellow
try {
    $mvnVersion = & mvn --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Maven is available" -ForegroundColor Green
    } else {
        Write-Host "Maven not found" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "Maven test failed: $_" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "All tests passed! Starting application..." -ForegroundColor Green
Write-Host "Press Ctrl+C to stop the application" -ForegroundColor Yellow
Write-Host ""

# Start the application
try {
    & mvn spring-boot:run
} catch {
    Write-Host "Application failed to start: $_" -ForegroundColor Red
    exit 1
}
