package com.grocease.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * Validates application configuration on startup to prevent runtime errors
 */
@Component
@Slf4j
public class StartupValidationConfig {

    @Value("${jwt.secret}")
    private String jwtSecret;

    @Value("${spring.servlet.multipart.max-file-size:10MB}")
    private String maxFileSize;

    @Value("${spring.servlet.multipart.file-size-threshold:2KB}")
    private String fileSizeThreshold;

    @EventListener(ApplicationReadyEvent.class)
    public void validateConfiguration() {
        log.info("🔍 Validating application configuration...");

        try {
            // Validate JWT Secret
            validateJwtSecret();
            
            // Validate File Upload Configuration
            validateFileUploadConfig();
            
            // Validate Buffer Configurations
            validateBufferConfigurations();
            
            log.info("✅ All configuration validations passed successfully!");
            
        } catch (Exception e) {
            log.error("❌ Configuration validation failed: {}", e.getMessage(), e);
            throw new IllegalStateException("Application configuration is invalid", e);
        }
    }

    private void validateJwtSecret() {
        if (jwtSecret == null || jwtSecret.trim().isEmpty()) {
            throw new IllegalStateException("JWT secret is not configured");
        }

        if (jwtSecret.length() < 32) {
            throw new IllegalStateException("JWT secret must be at least 32 characters long. Current: " + jwtSecret.length());
        }

        // Test byte array creation to prevent IndexOutOfBoundsException
        try {
            byte[] keyBytes = jwtSecret.getBytes();
            if (keyBytes.length < 32) {
                throw new IllegalStateException("JWT secret byte array is too short: " + keyBytes.length);
            }
            log.info("✅ JWT secret validation passed (length: {} chars, {} bytes)", jwtSecret.length(), keyBytes.length);
        } catch (Exception e) {
            throw new IllegalStateException("Failed to process JWT secret: " + e.getMessage(), e);
        }
    }

    private void validateFileUploadConfig() {
        log.info("📁 File upload configuration:");
        log.info("  - Max file size: {}", maxFileSize);
        log.info("  - File size threshold: {}", fileSizeThreshold);
        
        // Validate file size configurations don't cause buffer issues
        try {
            long maxSize = parseSize(maxFileSize);
            long threshold = parseSize(fileSizeThreshold);
            
            if (maxSize <= 0) {
                throw new IllegalStateException("Max file size must be positive: " + maxFileSize);
            }
            
            if (threshold <= 0) {
                throw new IllegalStateException("File size threshold must be positive: " + fileSizeThreshold);
            }
            
            if (threshold > maxSize) {
                log.warn("⚠️ File size threshold ({}) is larger than max file size ({})", fileSizeThreshold, maxFileSize);
            }
            
            log.info("✅ File upload configuration validation passed");
        } catch (Exception e) {
            throw new IllegalStateException("Invalid file upload configuration: " + e.getMessage(), e);
        }
    }

    private void validateBufferConfigurations() {
        // Test common buffer operations that might cause IndexOutOfBoundsException
        try {
            // Test string operations
            String testString = "Bearer test-token-12345";
            if (testString.length() > 7) {
                String extracted = testString.substring(7);
                log.debug("✅ String substring operation test passed");
            }
            
            // Test array operations
            byte[] testArray = new byte[1024];
            if (testArray.length >= 1024) {
                log.debug("✅ Array bounds test passed");
            }
            
            log.info("✅ Buffer configuration validation passed");
        } catch (IndexOutOfBoundsException e) {
            throw new IllegalStateException("Buffer configuration validation failed: " + e.getMessage(), e);
        }
    }

    private long parseSize(String size) {
        if (size == null || size.trim().isEmpty()) {
            return 0;
        }
        
        size = size.trim().toUpperCase();
        long multiplier = 1;
        
        if (size.endsWith("KB")) {
            multiplier = 1024;
            size = size.substring(0, size.length() - 2);
        } else if (size.endsWith("MB")) {
            multiplier = 1024 * 1024;
            size = size.substring(0, size.length() - 2);
        } else if (size.endsWith("GB")) {
            multiplier = 1024 * 1024 * 1024;
            size = size.substring(0, size.length() - 2);
        } else if (size.endsWith("B")) {
            size = size.substring(0, size.length() - 1);
        }
        
        try {
            return Long.parseLong(size.trim()) * multiplier;
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid size format: " + size, e);
        }
    }
}
