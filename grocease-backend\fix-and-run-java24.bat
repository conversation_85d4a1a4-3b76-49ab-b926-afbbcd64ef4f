@echo off
echo ========================================
echo GrocEase Backend - Java 24 Quick Fix
echo ========================================

REM Set environment variables
set JWT_SECRET=GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789
set EMAIL_VERIFICATION_ENABLED=false
set DB_USERNAME=postgres
set DB_PASSWORD=admin
set CLOUDINARY_ENABLED=true
set JAVA_HOME=C:\Program Files\Java\jdk-24

echo Environment configured for Java 24 with class format fix
echo.

REM Check Java version
echo Checking Java version...
java -version
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java not found!
    pause
    exit /b 1
)

echo.
echo Starting GrocEase Backend with Java 24 compatibility fix...
echo Application will be available at: http://localhost:8080/api
echo Press Ctrl+C to stop the application
echo.

REM Run the application with the fix
java --add-opens java.base/java.lang=ALL-UNNAMED ^
     --add-opens java.base/java.util=ALL-UNNAMED ^
     --add-opens java.base/java.lang.reflect=ALL-UNNAMED ^
     --add-opens java.base/java.text=ALL-UNNAMED ^
     --add-opens java.desktop/java.awt.font=ALL-UNNAMED ^
     --add-opens java.base/sun.nio.ch=ALL-UNNAMED ^
     --add-opens java.base/java.io=ALL-UNNAMED ^
     --add-opens java.base/java.nio=ALL-UNNAMED ^
     --add-opens java.base/sun.nio.cs=ALL-UNNAMED ^
     --add-opens java.base/sun.security.util=ALL-UNNAMED ^
     --add-opens java.base/sun.net.dns=ALL-UNNAMED ^
     --add-opens java.base/java.security=ALL-UNNAMED ^
     --enable-native-access=ALL-UNNAMED ^
     -Dspring.classformat.ignore=true ^
     -cp "target/classes;target/dependency/*" ^
     com.grocease.GrocEaseApplication

pause
