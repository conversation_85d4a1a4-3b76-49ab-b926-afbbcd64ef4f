#!/usr/bin/env pwsh

Write-Host "GrocEase Backend - Java 24 Compatible Runner" -ForegroundColor Green
Write-Host "===========================================" -ForegroundColor Green

# Set environment variables
$env:JWT_SECRET = "GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789"
$env:EMAIL_VERIFICATION_ENABLED = "false"
$env:DB_USERNAME = "postgres"
$env:DB_PASSWORD = "admin"
$env:CLOUDINARY_ENABLED = "true"
$env:PATH = "$env:PATH;C:\tools\maven\bin"

# Set Java 24 compatibility options
$env:MAVEN_OPTS = @"
-Djava.version=21
-Dmaven.compiler.source=21
-Dmaven.compiler.target=21
-Dmaven.compiler.release=21
-Dspring.classformat.ignore=true
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
--add-opens java.base/java.lang.reflect=ALL-UNNAMED
--add-opens java.base/java.text=ALL-UNNAMED
--add-opens java.desktop/java.awt.font=ALL-UNNAMED
--add-opens java.base/sun.nio.ch=ALL-UNNAMED
--add-opens java.base/java.io=ALL-UNNAMED
--add-opens java.base/java.nio=ALL-UNNAMED
--add-opens java.base/sun.nio.cs=ALL-UNNAMED
--add-opens java.base/sun.security.util=ALL-UNNAMED
--add-opens java.base/sun.net.dns=ALL-UNNAMED
--add-opens java.base/java.security=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED
--add-opens jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED
--enable-native-access=ALL-UNNAMED
-Dspring.profiles.active=dev
"@

Write-Host "Environment configured for Java 24 compatibility" -ForegroundColor Green

# Check Java version
Write-Host "Checking Java version..." -ForegroundColor Yellow
try {
    $javaVersion = & java -version 2>&1
    $versionLine = $javaVersion[0]
    Write-Host "Java Version: $versionLine" -ForegroundColor Cyan
} catch {
    Write-Host "ERROR: Java not found: $_" -ForegroundColor Red
    exit 1
}

# Check Maven
Write-Host "Checking Maven..." -ForegroundColor Yellow
try {
    $mvnVersion = & mvn --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Maven is available" -ForegroundColor Green
    } else {
        throw "Maven not found"
    }
} catch {
    Write-Host "ERROR: Maven not found. Please run install-maven.bat first" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "JAVA 24 COMPATIBILITY FEATURES:" -ForegroundColor Cyan
Write-Host "- Updated Spring Boot to 3.4.0 (Java 21+ support)" -ForegroundColor White
Write-Host "- Enhanced Maven compiler plugin (3.13.0)" -ForegroundColor White
Write-Host "- Added JVM module access arguments" -ForegroundColor White
Write-Host "- Updated Lombok to 1.18.34 (Java 21+ compatible)" -ForegroundColor White
Write-Host "- Configured native access permissions" -ForegroundColor White
Write-Host ""

Write-Host "Starting compilation with Java 24 compatibility..." -ForegroundColor Yellow

# Clean and compile
try {
    Write-Host "Cleaning previous build..." -ForegroundColor Yellow
    & mvn clean -q
    
    if ($LASTEXITCODE -ne 0) {
        throw "Clean failed"
    }
    
    Write-Host "Compiling with Java 24 compatibility..." -ForegroundColor Yellow
    & mvn compile -q
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Compilation failed. Checking detailed error..." -ForegroundColor Yellow
        & mvn compile
        throw "Compilation failed"
    }
    
    Write-Host "Compilation successful!" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Build failed: $_" -ForegroundColor Red
    Write-Host ""
    Write-Host "TROUBLESHOOTING:" -ForegroundColor Yellow
    Write-Host "1. Make sure PostgreSQL is running" -ForegroundColor White
    Write-Host "2. Check if port 8080 is available" -ForegroundColor White
    Write-Host "3. Verify Maven and Java are properly installed" -ForegroundColor White
    exit 1
}

Write-Host ""
Write-Host "Starting GrocEase Backend with Java 24..." -ForegroundColor Green
Write-Host "Application will be available at: http://localhost:8080/api" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to stop the application" -ForegroundColor Yellow
Write-Host ""

# Start the application
try {
    & mvn spring-boot:run
} catch {
    Write-Host "Application startup failed: $_" -ForegroundColor Red
    exit 1
}
