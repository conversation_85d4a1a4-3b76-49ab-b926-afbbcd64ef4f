# Network Request Logging

This app now includes comprehensive network request logging to help debug API issues and monitor network traffic.

## Features

### 1. **Automatic Request/Response Logging**
- All HTTP requests and responses are automatically logged
- Includes request details (URL, method, headers, body)
- Includes response details (status, headers, data, duration)
- Includes error details for failed requests

### 2. **Console Logging**
- Detailed logs appear in Metro bundler console
- Grouped logs for better readability
- Color-coded status indicators
- Request/response timing information

### 3. **In-App Network Debugger**
- Floating debug button (bug icon) in bottom-right corner
- Real-time network request monitoring
- Detailed request/response inspection
- Export logs functionality
- Auto-refresh toggle
- Clear logs functionality

### 4. **Test Network Logging**
- Built-in test button in API Test Component
- Generates sample network requests
- Verifies logging functionality

## How to Use

### Console Logging
1. Open Metro bundler terminal
2. Make any API request in the app
3. See detailed logs in console with format:
   ```
   🚀 GET http://********:8080/api/products
   ├── Request ID: req_1234567890_abc123
   ├── Headers: {...}
   └── Body: {...}
   
   ✅ 200 OK (245ms)
   ├── Request ID: req_1234567890_abc123
   └── Response Data: {...}
   ```

### In-App Debugger
1. Look for the floating bug icon (🐛) in bottom-right corner
2. Tap to open Network Debugger
3. View all network requests in real-time
4. Tap any request to see detailed information
5. Use toolbar buttons to:
   - ⏸️/▶️ Toggle auto-refresh
   - 🔄 Manual refresh
   - 📤 Export logs
   - 🗑️ Clear logs

### Test Network Logging
1. Go to Settings → API Test
2. Tap "Test Network Logging" button
3. Check console and debugger for generated logs

## Log Format

### Request Log
```json
{
  "id": "req_1234567890_abc123",
  "type": "REQUEST",
  "timestamp": "2025-07-11T14:30:00.000Z",
  "url": "http://********:8080/api/auth/login",
  "method": "POST",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer ..."
  },
  "body": "{\"email\":\"<EMAIL>\",\"password\":\"***\"}"
}
```

### Response Log
```json
{
  "id": "req_1234567890_abc123",
  "type": "RESPONSE",
  "timestamp": "2025-07-11T14:30:00.245Z",
  "status": 200,
  "statusText": "OK",
  "headers": {
    "content-type": "application/json"
  },
  "data": {
    "success": true,
    "token": "...",
    "user": {...}
  },
  "duration": "245ms"
}
```

### Error Log
```json
{
  "id": "req_1234567890_abc123",
  "type": "ERROR",
  "timestamp": "2025-07-11T14:30:00.100Z",
  "error": "Network connection failed",
  "duration": "100ms"
}
```

## API Configuration

The logging system automatically detects the platform and uses appropriate API URLs:

- **Android Emulator**: `http://********:8080/api`
- **iOS Simulator**: `http://localhost:8080/api`
- **Production**: `https://api.grocease.com/api`

## Troubleshooting

### No Logs Appearing
1. Check if Metro bundler is running
2. Verify network requests are being made
3. Try the "Test Network Logging" button

### Network Debugger Not Showing
1. Look for floating bug icon in bottom-right
2. Make sure NetworkDebugger is imported in App.tsx
3. Check for any console errors

### API Connection Issues
1. Check backend is running on port 8080
2. Verify correct API URL for your platform
3. Check network logs for connection errors

## Development Notes

- Network logs are stored in memory and cleared on app restart
- Logs include sensitive data - don't share in production
- Auto-refresh can be disabled to save performance
- Maximum log retention is handled automatically

## Files Modified

- `src/services/httpClient.ts` - Added logging functionality
- `src/components/NetworkDebugger.tsx` - In-app debugger component
- `src/components/ApiTestComponent.tsx` - Added test button
- `App.tsx` - Added NetworkDebugger component
- `src/constants/index.ts` - Updated API configuration
