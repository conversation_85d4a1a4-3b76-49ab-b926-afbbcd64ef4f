@echo off
echo ========================================
echo GrocEase Backend - IMMEDIATE FIX
echo ========================================

REM Set environment variables
set JWT_SECRET=GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789
set EMAIL_VERIFICATION_ENABLED=false
set DB_USERNAME=postgres
set DB_PASSWORD=admin
set CLOUDINARY_ENABLED=true

echo Starting application with class format fix...
echo.

REM Make sure we have dependencies
if not exist "target\dependency" (
    echo Copying dependencies...
    mvn dependency:copy-dependencies -DoutputDirectory=target/dependency -q
)

REM Run with the critical system property
java -Dspring.classformat.ignore=true ^
     --add-opens java.base/java.lang=ALL-UNNAMED ^
     --add-opens java.base/java.util=ALL-UNNAMED ^
     --add-opens java.base/java.lang.reflect=ALL-UNNAMED ^
     --enable-native-access=ALL-UNNAMED ^
     -cp "target/classes;target/dependency/*" ^
     com.grocease.GrocEaseApplication

pause
