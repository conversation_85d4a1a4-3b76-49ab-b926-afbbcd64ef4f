import React, { useState } from 'react';
import { View, Text, TouchableOpacity, Alert, ScrollView } from 'react-native';
import { storage } from '../services/storage';
import { httpClient } from '../services/httpClient';
import { authApi } from '../services/authApi';
import { API_CONFIG } from '../constants';

export const LoginDebugComponent: React.FC = () => {
  const [debugInfo, setDebugInfo] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addDebugInfo = (message: string) => {
    setDebugInfo(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    console.log(message);
  };

  const clearDebugInfo = () => {
    setDebugInfo([]);
  };

  const testStorageService = async () => {
    addDebugInfo('🔍 Testing Storage Service...');
    
    try {
      // Test if storage object exists
      if (!storage) {
        addDebugInfo('❌ Storage object is undefined');
        return false;
      }
      
      addDebugInfo('✅ Storage object exists');
      addDebugInfo(`Storage methods: ${Object.keys(storage).join(', ')}`);
      
      // Test getAuthToken method
      if (typeof storage.getAuthToken !== 'function') {
        addDebugInfo('❌ getAuthToken is not a function');
        return false;
      }
      
      addDebugInfo('✅ getAuthToken method exists');
      
      // Try to call getAuthToken
      const token = await storage.getAuthToken();
      addDebugInfo(`✅ getAuthToken returned: ${token ? 'token found' : 'no token'}`);
      
      return true;
    } catch (error) {
      addDebugInfo(`❌ Storage test failed: ${error}`);
      return false;
    }
  };

  const testAPIConfig = () => {
    addDebugInfo('🔍 Testing API Config...');
    
    try {
      if (!API_CONFIG) {
        addDebugInfo('❌ API_CONFIG is undefined');
        return false;
      }
      
      addDebugInfo(`✅ API_CONFIG exists`);
      addDebugInfo(`Base URL: ${API_CONFIG.BASE_URL}`);
      addDebugInfo(`Timeout: ${API_CONFIG.TIMEOUT}`);
      addDebugInfo(`Retry attempts: ${API_CONFIG.RETRY_ATTEMPTS}`);
      
      return true;
    } catch (error) {
      addDebugInfo(`❌ API Config test failed: ${error}`);
      return false;
    }
  };

  const testHttpClient = async () => {
    addDebugInfo('🔍 Testing HTTP Client...');
    
    try {
      if (!httpClient) {
        addDebugInfo('❌ httpClient is undefined');
        return false;
      }
      
      addDebugInfo('✅ httpClient exists');
      
      // Test a simple GET request to categories (should work without auth)
      const response = await httpClient.get('/categories');
      addDebugInfo(`✅ HTTP Client test successful: ${JSON.stringify(response).substring(0, 100)}...`);
      
      return true;
    } catch (error) {
      addDebugInfo(`❌ HTTP Client test failed: ${error}`);
      return false;
    }
  };

  const testLoginAPI = async () => {
    addDebugInfo('🔍 Testing Login API...');
    
    try {
      const credentials = {
        email: '<EMAIL>',
        password: 'password123'
      };
      
      addDebugInfo(`Attempting login with: ${credentials.email}`);
      
      const response = await authApi.login(credentials);
      
      if (response.success) {
        addDebugInfo('✅ Login successful!');
        addDebugInfo(`Token received: ${response.data.token ? 'Yes' : 'No'}`);
        addDebugInfo(`User: ${response.data.user?.email || 'Unknown'}`);
      } else {
        addDebugInfo(`❌ Login failed: ${response.message}`);
      }
      
      return response.success;
    } catch (error) {
      addDebugInfo(`❌ Login API test failed: ${error}`);
      return false;
    }
  };

  const runAllTests = async () => {
    if (isRunning) return;
    
    setIsRunning(true);
    clearDebugInfo();
    
    addDebugInfo('🚀 Starting comprehensive debug tests...');
    
    const storageOk = await testStorageService();
    const configOk = testAPIConfig();
    const httpOk = await testHttpClient();
    const loginOk = await testLoginAPI();
    
    addDebugInfo('\n📊 Test Summary:');
    addDebugInfo(`Storage Service: ${storageOk ? '✅' : '❌'}`);
    addDebugInfo(`API Config: ${configOk ? '✅' : '❌'}`);
    addDebugInfo(`HTTP Client: ${httpOk ? '✅' : '❌'}`);
    addDebugInfo(`Login API: ${loginOk ? '✅' : '❌'}`);
    
    if (storageOk && configOk && httpOk && loginOk) {
      addDebugInfo('\n🎉 All tests passed! Login should work.');
    } else {
      addDebugInfo('\n⚠️ Some tests failed. Check the details above.');
    }
    
    setIsRunning(false);
  };

  return (
    <View className="flex-1 bg-white p-4">
      <Text className="text-2xl font-bold text-gray-900 mb-4">Login Debug Tool</Text>
      
      <View className="flex-row gap-2 mb-4">
        <TouchableOpacity
          onPress={runAllTests}
          disabled={isRunning}
          className={`flex-1 p-3 rounded-lg ${isRunning ? 'bg-gray-300' : 'bg-blue-500'}`}
        >
          <Text className="text-white text-center font-semibold">
            {isRunning ? 'Running...' : 'Run All Tests'}
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={clearDebugInfo}
          className="px-4 py-3 bg-gray-500 rounded-lg"
        >
          <Text className="text-white font-semibold">Clear</Text>
        </TouchableOpacity>
      </View>
      
      <View className="flex-row gap-2 mb-4">
        <TouchableOpacity
          onPress={testStorageService}
          className="flex-1 p-2 bg-green-500 rounded"
        >
          <Text className="text-white text-center text-sm">Storage</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={testAPIConfig}
          className="flex-1 p-2 bg-green-500 rounded"
        >
          <Text className="text-white text-center text-sm">Config</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={testHttpClient}
          className="flex-1 p-2 bg-green-500 rounded"
        >
          <Text className="text-white text-center text-sm">HTTP</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          onPress={testLoginAPI}
          className="flex-1 p-2 bg-green-500 rounded"
        >
          <Text className="text-white text-center text-sm">Login</Text>
        </TouchableOpacity>
      </View>
      
      <ScrollView className="flex-1 bg-gray-100 p-3 rounded-lg">
        {debugInfo.length === 0 ? (
          <Text className="text-gray-500 italic">No debug info yet. Run tests to see results.</Text>
        ) : (
          debugInfo.map((info, index) => (
            <Text key={index} className="text-sm text-gray-800 mb-1 font-mono">
              {info}
            </Text>
          ))
        )}
      </ScrollView>
      
      <View className="mt-4 p-3 bg-blue-50 rounded-lg">
        <Text className="text-sm text-blue-800">
          <Text className="font-semibold">Instructions:</Text> Run the tests to identify the login issue. 
          Check console logs for additional details. If storage test fails, the issue is with AsyncStorage setup.
        </Text>
      </View>
    </View>
  );
};
