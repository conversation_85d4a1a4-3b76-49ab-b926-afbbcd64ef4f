@echo off
echo GrocEase Backend - Simple Java 24 Runner
echo =========================================

REM Set environment variables
set JWT_SECRET=GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789
set EMAIL_VERIFICATION_ENABLED=false
set DB_USERNAME=postgres
set DB_PASSWORD=admin
set CLOUDINARY_ENABLED=true
set PATH=%PATH%;C:\tools\maven\bin

REM Clear any problematic Maven options
set MAVEN_OPTS=

echo Environment configured for Java 24
echo.

REM Check Java version
echo Current Java version:
java -version
echo.

echo Configuration:
echo - Java: 24
echo - Spring Boot: 3.3.6
echo - Maven Compiler: 3.13.0
echo.

echo Attempting compilation with Java 24...
mvn clean compile

if %ERRORLEVEL% neq 0 (
    echo.
    echo ========================================
    echo COMPILATION FAILED
    echo ========================================
    echo.
    echo This is expected with Java 24 due to:
    echo 1. Maven ecosystem not fully supporting Java 24 yet
    echo 2. Some dependencies may not be compatible
    echo.
    echo RECOMMENDED SOLUTIONS:
    echo 1. Use IntelliJ IDEA - it handles Java versions better
    echo 2. Install Java 17 alongside Java 24
    echo 3. Use Docker with Java 17
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo COMPILATION SUCCESSFUL!
echo ========================================
echo.
echo Starting application with Java 24...
echo Application URL: http://localhost:8080/api
echo.

mvn spring-boot:run

pause
