@echo off
echo GrocEase Backend - Java 24 Compatible Runner
echo ===========================================

REM Set environment variables
set JWT_SECRET=GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789
set EMAIL_VERIFICATION_ENABLED=false
set DB_USERNAME=postgres
set DB_PASSWORD=admin
set CLOUDINARY_ENABLED=true
set PATH=%PATH%;C:\tools\maven\bin

REM Set Java 24 compatibility options
set MAVEN_OPTS=-Djava.version=21 -Dmaven.compiler.source=21 -Dmaven.compiler.target=21 -Dmaven.compiler.release=21 -Dspring.classformat.ignore=true --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.text=ALL-UNNAMED --add-opens java.desktop/java.awt.font=ALL-UNNAMED --add-opens java.base/sun.nio.ch=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/sun.nio.cs=ALL-UNNAMED --add-opens java.base/sun.security.util=ALL-UNNAMED --add-opens java.base/sun.net.dns=ALL-UNNAMED --add-opens java.base/java.security=ALL-UNNAMED --add-opens jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED --add-opens jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED --add-opens jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --enable-native-access=ALL-UNNAMED

echo Environment configured for Java 24 compatibility

REM Check Java version
echo Checking Java version...
java -version
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java not found!
    pause
    exit /b 1
)

REM Check Maven
echo Checking Maven...
where mvn >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Maven not found. Please run install-maven.bat first
    pause
    exit /b 1
)

echo.
echo JAVA 24 COMPATIBILITY FEATURES:
echo - Updated Spring Boot to 3.4.0 (Java 21+ support)
echo - Enhanced Maven compiler plugin (3.13.0)
echo - Added JVM module access arguments
echo - Updated Lombok to 1.18.34 (Java 21+ compatible)
echo - Configured native access permissions
echo.

echo Starting compilation with Java 24 compatibility...

REM Clean and compile
echo Cleaning previous build...
mvn clean -q
if %ERRORLEVEL% neq 0 (
    echo ERROR: Clean failed
    pause
    exit /b 1
)

echo Compiling with Java 24 compatibility...
mvn compile -q
if %ERRORLEVEL% neq 0 (
    echo Compilation failed. Showing detailed error...
    mvn compile
    echo.
    echo TROUBLESHOOTING:
    echo 1. Make sure PostgreSQL is running
    echo 2. Check if port 8080 is available
    echo 3. Verify Maven and Java are properly installed
    pause
    exit /b 1
)

echo Compilation successful!
echo.
echo Starting GrocEase Backend with Java 24...
echo Application will be available at: http://localhost:8080/api
echo Press Ctrl+C to stop the application
echo.

REM Start the application
mvn spring-boot:run

pause
