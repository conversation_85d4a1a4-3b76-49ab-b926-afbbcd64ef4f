<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="0d41ecc8-f15d-4ae9-8806-710cd40a7bfe" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../../../../../Program Files/Java/jdk-24/lib/src.zip!/java.base/java/lang/ExceptionInInitializerError.java" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 1
}</component>
  <component name="ProjectId" id="2zX5UoJ1ug3m9ScrYXQKO4x2Vl5" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.GrocEaseApplication.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "RunOnceActivity.git.unshallow": "true",
    "git-widget-placeholder": "master",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "C:/Users/<USER>/Desktop/ashish/Grocery-app/grocease-backend",
    "project.structure.last.edited": "SDKs",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.2",
    "settings.editor.selected.configurable": "reference.settingsdialog.IDE.editor.colors"
  }
}]]></component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="backend" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="0d41ecc8-f15d-4ae9-8806-710cd40a7bfe" name="Changes" comment="" />
      <created>1751864596666</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751864596666</updated>
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>