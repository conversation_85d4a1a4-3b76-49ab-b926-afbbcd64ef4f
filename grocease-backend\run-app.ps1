#!/usr/bin/env pwsh

Write-Host "Starting GrocEase Backend Application..." -ForegroundColor Green

# Set environment variables for development
$env:JWT_SECRET = "GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789"
$env:EMAIL_VERIFICATION_ENABLED = "false"
$env:DB_USERNAME = "postgres"
$env:DB_PASSWORD = "admin"
$env:CLOUDINARY_ENABLED = "true"

# Add Maven to PATH for this session
$env:PATH = "$env:PATH;C:\tools\maven\bin"

Write-Host "Checking Maven installation..." -ForegroundColor Yellow
try {
    $mvnVersion = & mvn --version 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Maven found:" -ForegroundColor Green
        Write-Host $mvnVersion
    } else {
        throw "Maven not found"
    }
} catch {
    Write-Host "ERROR: Maven not found in PATH!" -ForegroundColor Red
    Write-Host "Please restart your PowerShell or run install-maven.bat first" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Starting application with Spring Boot..." -ForegroundColor Green
Write-Host "Environment: Development" -ForegroundColor Cyan
Write-Host "Database: PostgreSQL (localhost:5432/grocease_db)" -ForegroundColor Cyan
Write-Host "Email Verification: Disabled" -ForegroundColor Cyan
Write-Host ""

# Run the application
try {
    & mvn spring-boot:run
} catch {
    Write-Host "Error running application: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Read-Host "Press Enter to exit"
