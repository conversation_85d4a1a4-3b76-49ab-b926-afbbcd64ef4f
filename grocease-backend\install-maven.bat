@echo off
echo Installing Apache Maven...

REM Check if Ma<PERSON> is already installed
where mvn >nul 2>&1
if %ERRORLEVEL% == 0 (
    echo Maven is already installed!
    mvn --version
    goto :end
)

REM Check if Java is installed
where java >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java is not installed or not in PATH!
    echo Please install Java 17 first from: https://adoptium.net/
    pause
    exit /b 1
)

echo Java found:
java -version

REM Create tools directory
if not exist "C:\tools" mkdir "C:\tools"

REM Download Maven
echo Downloading Maven 3.9.6...
powershell -Command "& {Invoke-WebRequest -Uri 'https://archive.apache.org/dist/maven/maven-3/3.9.6/binaries/apache-maven-3.9.6-bin.zip' -OutFile 'C:\tools\maven.zip'}"

if not exist "C:\tools\maven.zip" (
    echo ERROR: Failed to download Maven!
    pause
    exit /b 1
)

REM Extract Maven
echo Extracting Maven...
powershell -Command "& {Expand-Archive -Path 'C:\tools\maven.zip' -DestinationPath 'C:\tools' -Force}"

REM Rename directory
if exist "C:\tools\apache-maven-3.9.6" (
    if exist "C:\tools\maven" rmdir /s /q "C:\tools\maven"
    ren "C:\tools\apache-maven-3.9.6" "maven"
)

REM Add to PATH
echo Adding Maven to PATH...
setx PATH "%PATH%;C:\tools\maven\bin" /M

REM Clean up
del "C:\tools\maven.zip"

echo.
echo ✅ Maven installation completed!
echo.
echo Please RESTART your command prompt or PowerShell and then run:
echo   mvn --version
echo   mvn spring-boot:run
echo.

:end
pause
