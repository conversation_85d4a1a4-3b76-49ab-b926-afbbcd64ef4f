@echo off
echo GrocEase Backend - Java 24 Direct Runner
echo =========================================

REM Set environment variables
set JWT_SECRET=GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789
set EMAIL_VERIFICATION_ENABLED=false
set DB_USERNAME=postgres
set DB_PASSWORD=admin
set CLOUDINARY_ENABLED=true
set PATH=%PATH%;C:\tools\maven\bin

echo Environment configured for Java 24
echo.

echo Current Java version:
java -version
echo.

echo Step 1: Compiling with Java 24...
mvn clean compile

if %ERRORLEVEL% neq 0 (
    echo ERROR: Compilation failed!
    pause
    exit /b 1
)

echo.
echo ✅ Compilation successful with Java 24!
echo.

echo Step 2: Packaging the application...
mvn package -DskipTests

if %ERRORLEVEL% neq 0 (
    echo ERROR: Packaging failed!
    pause
    exit /b 1
)

echo.
echo ✅ Packaging successful!
echo.

echo Step 3: Running application directly with Java 24...
echo Application will be available at: http://localhost:8080/api
echo Press Ctrl+C to stop the application
echo.

REM Run the JAR directly with Java 24
java --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.text=ALL-UNNAMED --add-opens java.desktop/java.awt.font=ALL-UNNAMED --add-opens java.base/sun.nio.ch=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/sun.nio.cs=ALL-UNNAMED --add-opens java.base/sun.security.util=ALL-UNNAMED --add-opens java.base/sun.net.dns=ALL-UNNAMED --add-opens java.base/java.security=ALL-UNNAMED -jar target\grocease-backend-0.0.1-SNAPSHOT.jar

pause
