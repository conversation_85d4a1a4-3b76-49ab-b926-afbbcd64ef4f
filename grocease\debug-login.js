// Debug script to test the login issue
// Run this with: node debug-login.js

console.log('🔍 Debugging login issue...\n');

// Test 1: Check if the storage export works
try {
  console.log('1. Testing storage import...');
  
  // Simulate the storage object structure
  const mockStorage = {
    getAuthToken: async () => {
      console.log('   getAuthToken called');
      return null;
    },
    getRefreshToken: async () => {
      console.log('   getRefreshToken called');
      return null;
    }
  };
  
  console.log('   ✅ Storage object structure is correct');
  console.log('   Storage methods:', Object.keys(mockStorage));
  
} catch (error) {
  console.log('   ❌ Storage import failed:', error.message);
}

// Test 2: Check API_CONFIG
try {
  console.log('\n2. Testing API_CONFIG...');
  
  // Simulate __DEV__ environment
  const __DEV__ = true;
  
  const API_CONFIG = {
    BASE_URL: __DEV__ ? 'http://localhost:8080/api' : 'https://api.grocease.com/api',
    TIMEOUT: 10000,
    RETRY_ATTEMPTS: 3,
  };
  
  console.log('   ✅ API_CONFIG created successfully');
  console.log('   BASE_URL:', API_CONFIG.BASE_URL);
  console.log('   TIMEOUT:', API_CONFIG.TIMEOUT);
  
} catch (error) {
  console.log('   ❌ API_CONFIG failed:', error.message);
}

// Test 3: Check HTTP client structure
try {
  console.log('\n3. Testing HTTP client structure...');
  
  class MockHttpClient {
    constructor() {
      this.baseURL = 'http://localhost:8080/api';
      this.timeout = 10000;
    }
    
    async getAuthHeaders() {
      console.log('   getAuthHeaders called');
      // This is where the error might occur
      const token = null; // Simulate no token
      const headers = {
        'Content-Type': 'application/json',
      };
      
      if (token) {
        headers.Authorization = `Bearer ${token}`;
      }
      
      return headers;
    }
  }
  
  const client = new MockHttpClient();
  const headers = await client.getAuthHeaders();
  
  console.log('   ✅ HTTP client structure is correct');
  console.log('   Headers:', headers);
  
} catch (error) {
  console.log('   ❌ HTTP client failed:', error.message);
}

// Test 4: Check login flow
try {
  console.log('\n4. Testing login flow...');
  
  const mockAuthApi = {
    login: async (credentials) => {
      console.log('   Login called with:', credentials);
      
      // Simulate HTTP request
      const mockResponse = {
        success: true,
        data: {
          user: { id: '1', email: credentials.email },
          token: 'mock-jwt-token',
          refreshToken: 'mock-refresh-token'
        },
        message: 'Login successful'
      };
      
      return mockResponse;
    }
  };
  
  const result = await mockAuthApi.login({
    email: '<EMAIL>',
    password: 'password123'
  });
  
  console.log('   ✅ Login flow completed');
  console.log('   Result:', result);
  
} catch (error) {
  console.log('   ❌ Login flow failed:', error.message);
}

console.log('\n🎯 Debugging complete!');
console.log('\nIf all tests passed, the issue might be:');
console.log('1. React Native environment not properly set up');
console.log('2. AsyncStorage not available');
console.log('3. Import path issues in the actual app');
console.log('4. Expo/React Native version compatibility');

console.log('\n💡 Try these solutions:');
console.log('1. Clear Metro cache: npx expo start --clear');
console.log('2. Restart the development server');
console.log('3. Check if AsyncStorage is properly installed');
console.log('4. Verify all imports are correct');
