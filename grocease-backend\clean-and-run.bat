@echo off
echo ========================================
echo GrocEase Backend - CLEAN AND RUN
echo ========================================

REM Set environment variables
set JWT_SECRET=GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789
set EMAIL_VERIFICATION_ENABLED=false
set DB_USERNAME=postgres
set DB_PASSWORD=admin
set CLOUDINARY_ENABLED=true
set JAVA_HOME=C:\Program Files\Java\jdk-24

echo Step 1: Cleaning old compiled files...
if exist "target" rmdir /s /q target
echo Cleaned target directory.

echo.
echo Step 2: Compiling with Java 24...
mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Copying dependencies...
mvn dependency:copy-dependencies -DoutputDirectory=target/dependency -q

echo.
echo Step 4: Starting application with compatibility fix...
java -Dspring.classformat.ignore=true ^
     --add-opens java.base/java.lang=ALL-UNNAMED ^
     --add-opens java.base/java.util=ALL-UNNAMED ^
     --add-opens java.base/java.lang.reflect=ALL-UNNAMED ^
     --add-opens java.base/java.text=ALL-UNNAMED ^
     --add-opens java.desktop/java.awt.font=ALL-UNNAMED ^
     --add-opens java.base/sun.nio.ch=ALL-UNNAMED ^
     --add-opens java.base/java.io=ALL-UNNAMED ^
     --add-opens java.base/java.nio=ALL-UNNAMED ^
     --add-opens java.base/sun.nio.cs=ALL-UNNAMED ^
     --add-opens java.base/sun.security.util=ALL-UNNAMED ^
     --add-opens java.base/sun.net.dns=ALL-UNNAMED ^
     --add-opens java.base/java.security=ALL-UNNAMED ^
     --enable-native-access=ALL-UNNAMED ^
     -cp "target/classes;target/dependency/*" ^
     com.grocease.GrocEaseApplication

pause
