@echo off
echo Starting GrocEase Backend with Java 24...
echo ==========================================

REM Set environment variables
set JWT_SECRET=GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789
set EMAIL_VERIFICATION_ENABLED=false
set DB_USERNAME=postgres
set DB_PASSWORD=admin
set CLOUDINARY_ENABLED=true
set PATH=%PATH%;C:\tools\maven\bin

REM Set Java 24 specific JVM arguments
set MAVEN_OPTS=--add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang.reflect=ALL-UNNAMED --add-opens java.base/java.text=ALL-UNNAMED --add-opens java.desktop/java.awt.font=ALL-UNNAMED --add-opens java.base/sun.nio.ch=ALL-UNNAMED --add-opens java.base/java.io=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/sun.nio.cs=ALL-UNNAMED --add-opens java.base/sun.security.util=ALL-UNNAMED --add-opens java.base/sun.net.dns=ALL-UNNAMED --add-opens java.base/java.security=ALL-UNNAMED --add-opens jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED --add-opens jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED --add-opens jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --enable-native-access=ALL-UNNAMED

echo Java 24 environment configured
echo.

REM Check Java version
echo Checking Java version...
java -version
if %ERRORLEVEL% neq 0 (
    echo ERROR: Java not found!
    pause
    exit /b 1
)

echo.
echo Maven configuration for Java 24:
echo - Spring Boot: 3.3.6
echo - Maven Compiler Plugin: 3.13.0
echo - Java Source/Target: 24
echo - Added module access arguments
echo.

echo Starting compilation...
mvn clean compile

if %ERRORLEVEL% neq 0 (
    echo.
    echo Compilation failed. This might be due to:
    echo 1. Java 24 compatibility issues with some dependencies
    echo 2. Maven plugin limitations
    echo.
    echo Try running with IntelliJ IDEA instead, which handles Java versions better.
    pause
    exit /b 1
)

echo.
echo Compilation successful! Starting application...
echo Application will be available at: http://localhost:8080/api
echo.

mvn spring-boot:run

pause
