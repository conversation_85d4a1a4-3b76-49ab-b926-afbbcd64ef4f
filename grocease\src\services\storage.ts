import AsyncStorage from '@react-native-async-storage/async-storage';

// Temporarily disable Keychain to avoid null method errors
// This can be re-enabled once the Android Keychain issue is resolved
let Keychain: any = null;
let isKeychainAvailable = false;

console.log('Using AsyncStorage for secure storage (Keychain temporarily disabled)');

// Simple function that always returns false for now
const testKeychainAvailability = async (): Promise<boolean> => {
  return false;
};

const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_DATA: 'user_data',
  ONBOARDING_COMPLETED: 'onboarding_completed',
  CART_DATA: 'cart_data',
  SEARCH_HISTORY: 'search_history',
  THEME_MODE: 'theme_mode',
} as const;

const KEYCHAIN_SERVICE = 'GroceEase';

export class StorageService {
  // Secure token storage using Keychain (iOS) / Keystore (Android)
  static async storeTokens(token: string, refreshToken: string): Promise<void> {
    // Test keychain availability at runtime
    const keychainAvailable = await testKeychainAvailability();

    // Try Keychain first if available
    if (keychainAvailable && Keychain && Keychain.setInternetCredentials) {
      try {
        await Keychain.setInternetCredentials(
          KEYCHAIN_SERVICE,
          'auth_tokens',
          JSON.stringify({ token, refreshToken })
        );
        console.log('Tokens stored successfully in Keychain');
        return;
      } catch (error) {
        console.error('Error storing tokens in Keychain:', error);
        // Fall through to AsyncStorage
      }
    }

    // Fallback to AsyncStorage
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, token);
      await AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
      console.log('Tokens stored successfully in AsyncStorage');
    } catch (error) {
      console.error('Error storing tokens in AsyncStorage:', error);
      throw error;
    }
  }

  static async getTokens(): Promise<{ token: string; refreshToken: string } | null> {
    // Test keychain availability at runtime
    const keychainAvailable = await testKeychainAvailability();

    // Try Keychain first if available
    if (keychainAvailable && Keychain && Keychain.getInternetCredentials) {
      try {
        const credentials = await Keychain.getInternetCredentials(KEYCHAIN_SERVICE);
        if (credentials && credentials.password) {
          const tokens = JSON.parse(credentials.password);
          console.log('Tokens retrieved successfully from Keychain');
          return tokens;
        }
      } catch (error) {
        console.error('Error getting tokens from Keychain:', error);
        // Fall through to AsyncStorage
      }
    }

    // Fallback to AsyncStorage
    try {
      const token = await AsyncStorage.getItem(STORAGE_KEYS.AUTH_TOKEN);
      const refreshToken = await AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
      if (token && refreshToken) {
        console.log('Tokens retrieved successfully from AsyncStorage');
        return { token, refreshToken };
      }
    } catch (asyncError) {
      console.error('Error getting tokens from AsyncStorage:', asyncError);
    }

    console.log('No tokens found in storage');
    return null;
  }

  static async getAuthToken(): Promise<string | null> {
    const tokens = await this.getTokens();
    return tokens?.token || null;
  }

  static async getRefreshToken(): Promise<string | null> {
    const tokens = await this.getTokens();
    return tokens?.refreshToken || null;
  }

  static async removeTokens(): Promise<void> {
    // Test keychain availability at runtime
    const keychainAvailable = await testKeychainAvailability();

    // Try Keychain first if available
    if (keychainAvailable && Keychain && Keychain.resetInternetCredentials) {
      try {
        await Keychain.resetInternetCredentials(KEYCHAIN_SERVICE);
        console.log('Tokens removed successfully from Keychain');
      } catch (error) {
        console.error('Error removing tokens from Keychain:', error);
      }
    }

    // Also remove from AsyncStorage
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.AUTH_TOKEN,
        STORAGE_KEYS.REFRESH_TOKEN,
      ]);
      console.log('Tokens removed successfully from AsyncStorage');
    } catch (error) {
      console.error('Error removing tokens from AsyncStorage:', error);
    }
  }

  // User data storage
  static async storeUserData(userData: any): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
    } catch (error) {
      console.error('Error storing user data:', error);
    }
  }

  static async getUserData(): Promise<any | null> {
    try {
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER_DATA);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  }

  static async removeUserData(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA);
    } catch (error) {
      console.error('Error removing user data:', error);
    }
  }

  // Onboarding status
  static async setOnboardingCompleted(completed: boolean): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.ONBOARDING_COMPLETED, JSON.stringify(completed));
    } catch (error) {
      console.error('Error setting onboarding status:', error);
    }
  }

  static async isOnboardingCompleted(): Promise<boolean> {
    try {
      const completed = await AsyncStorage.getItem(STORAGE_KEYS.ONBOARDING_COMPLETED);
      return completed ? JSON.parse(completed) : false;
    } catch (error) {
      console.error('Error getting onboarding status:', error);
      return false;
    }
  }

  // Cart data persistence
  static async storeCartData(cartData: any): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.CART_DATA, JSON.stringify(cartData));
    } catch (error) {
      console.error('Error storing cart data:', error);
    }
  }

  static async getCartData(): Promise<any | null> {
    try {
      const cartData = await AsyncStorage.getItem(STORAGE_KEYS.CART_DATA);
      return cartData ? JSON.parse(cartData) : null;
    } catch (error) {
      console.error('Error getting cart data:', error);
      return null;
    }
  }

  static async removeCartData(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.CART_DATA);
    } catch (error) {
      console.error('Error removing cart data:', error);
    }
  }

  // Search history
  static async storeSearchHistory(searches: string[]): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.SEARCH_HISTORY, JSON.stringify(searches));
    } catch (error) {
      console.error('Error storing search history:', error);
    }
  }

  static async getSearchHistory(): Promise<string[]> {
    try {
      const history = await AsyncStorage.getItem(STORAGE_KEYS.SEARCH_HISTORY);
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.error('Error getting search history:', error);
      return [];
    }
  }

  // Theme mode storage
  static async setThemeMode(mode: string): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.THEME_MODE, mode);
    } catch (error) {
      console.error('Error setting theme mode:', error);
    }
  }

  static async getThemeMode(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.THEME_MODE);
    } catch (error) {
      console.error('Error getting theme mode:', error);
      return null;
    }
  }

  // Clear all data (logout)
  static async clearAllData(): Promise<void> {
    try {
      await Promise.all([
        this.removeTokens(),
        this.removeUserData(),
        this.removeCartData(),
        AsyncStorage.multiRemove([
          STORAGE_KEYS.SEARCH_HISTORY,
          // Don't clear theme mode on logout
        ]),
      ]);
    } catch (error) {
      console.error('Error clearing all data:', error);
    }
  }
}

// Export an instance for easier usage
export const storage = {
  getAuthToken: async () => {
    try {
      return await StorageService.getAuthToken();
    } catch (error) {
      console.error('Error in storage.getAuthToken:', error);
      return null;
    }
  },
  getRefreshToken: async () => {
    try {
      return await StorageService.getRefreshToken();
    } catch (error) {
      console.error('Error in storage.getRefreshToken:', error);
      return null;
    }
  },
  storeTokens: (token: string, refreshToken: string) => StorageService.storeTokens(token, refreshToken),
  removeTokens: () => StorageService.removeTokens(),
  getUserData: () => StorageService.getUserData(),
  storeUserData: (userData: any) => StorageService.storeUserData(userData),
  removeUserData: () => StorageService.removeUserData(),
  getCartData: () => StorageService.getCartData(),
  storeCartData: (cartData: any) => StorageService.storeCartData(cartData),
  removeCartData: () => StorageService.removeCartData(),
  getOnboardingCompleted: () => StorageService.getOnboardingCompleted(),
  setOnboardingCompleted: (completed: boolean) => StorageService.setOnboardingCompleted(completed),
  addSearchTerm: (term: string) => StorageService.addSearchTerm(term),
  getSearchHistory: () => StorageService.getSearchHistory(),
  setThemeMode: (mode: string) => StorageService.setThemeMode(mode),
  getThemeMode: () => StorageService.getThemeMode(),
  clearAllData: () => StorageService.clearAllData(),
};

// Debug log to verify export
console.log('Storage service exported successfully with methods:', Object.keys(storage));
