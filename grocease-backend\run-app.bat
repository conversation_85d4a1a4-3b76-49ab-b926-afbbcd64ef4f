@echo off
echo Starting GrocEase Backend Application...

REM Set Java 17 for this session
set JAVA_HOME=C:\Program Files\Java\jdk-17
set PATH=%JAVA_HOME%\bin;%PATH%

REM Set environment variables for development
set JWT_SECRET=GrocEase2024SecureJwtSecretKeyForDevelopmentAndProductionUse123456789
set EMAIL_VERIFICATION_ENABLED=false
set DB_USERNAME=postgres
set DB_PASSWORD=admin
set CLOUDINARY_ENABLED=true

REM Add Maven to PATH for this session
set PATH=%PATH%;C:\tools\maven\bin

echo Checking Maven installation...
where mvn >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo ERROR: Maven not found in PATH!
    echo Please restart your command prompt or run install-maven.bat first
    pause
    exit /b 1
)

echo Maven found:
mvn --version

echo.
echo Starting application with Spring Boot...
echo Environment: Development
echo Database: PostgreSQL (localhost:5432/grocease_db)
echo Email Verification: Disabled
echo.

mvn spring-boot:run

pause
