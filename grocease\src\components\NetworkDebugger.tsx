import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Modal,
  Alert,
  Share,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { httpClient } from '../services/httpClient';

interface NetworkLog {
  id: string;
  type: 'REQUEST' | 'RESPONSE' | 'ERROR';
  timestamp: string;
  url?: string;
  method?: string;
  status?: number;
  duration?: string;
  headers?: any;
  body?: any;
  data?: any;
  error?: any;
}

const NetworkDebugger: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [logs, setLogs] = useState<NetworkLog[]>([]);
  const [selectedLog, setSelectedLog] = useState<NetworkLog | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    if (autoRefresh && isVisible) {
      const interval = setInterval(() => {
        setLogs(httpClient.getNetworkLogs());
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [autoRefresh, isVisible]);

  const refreshLogs = () => {
    setLogs(httpClient.getNetworkLogs());
  };

  const clearLogs = () => {
    httpClient.clearNetworkLogs();
    setLogs([]);
    setSelectedLog(null);
  };

  const exportLogs = async () => {
    try {
      const logsText = JSON.stringify(logs, null, 2);
      await Share.share({
        message: logsText,
        title: 'Network Logs Export',
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to export logs');
    }
  };

  const getStatusColor = (status?: number) => {
    if (!status) return '#6B7280';
    if (status >= 200 && status < 300) return '#10B981';
    if (status >= 400 && status < 500) return '#F59E0B';
    if (status >= 500) return '#EF4444';
    return '#6B7280';
  };

  const getMethodColor = (method?: string) => {
    switch (method?.toUpperCase()) {
      case 'GET': return '#3B82F6';
      case 'POST': return '#10B981';
      case 'PUT': return '#F59E0B';
      case 'DELETE': return '#EF4444';
      case 'PATCH': return '#8B5CF6';
      default: return '#6B7280';
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const renderLogItem = (log: NetworkLog, index: number) => {
    const isRequest = log.type === 'REQUEST';
    const isError = log.type === 'ERROR';
    
    return (
      <TouchableOpacity
        key={`${log.id}-${index}`}
        className="bg-white border border-gray-200 rounded-lg p-3 mb-2"
        onPress={() => setSelectedLog(log)}
      >
        <View className="flex-row items-center justify-between mb-2">
          <View className="flex-row items-center">
            <View 
              className="w-2 h-2 rounded-full mr-2"
              style={{ 
                backgroundColor: isError ? '#EF4444' : isRequest ? '#3B82F6' : getStatusColor(log.status) 
              }}
            />
            <Text className="text-xs text-gray-500">{formatTime(log.timestamp)}</Text>
          </View>
          {log.duration && (
            <Text className="text-xs text-gray-500">{log.duration}</Text>
          )}
        </View>

        {isRequest && (
          <View className="flex-row items-center mb-1">
            <Text 
              className="text-xs font-bold mr-2 px-2 py-1 rounded"
              style={{ 
                backgroundColor: getMethodColor(log.method),
                color: 'white'
              }}
            >
              {log.method}
            </Text>
            <Text className="text-sm flex-1" numberOfLines={1}>
              {log.url?.replace(httpClient.getNetworkLogs().length > 0 ? 'http://10.0.2.2:8080/api' : '', '') || ''}
            </Text>
          </View>
        )}

        {!isRequest && log.status && (
          <View className="flex-row items-center mb-1">
            <Text 
              className="text-xs font-bold mr-2 px-2 py-1 rounded"
              style={{ 
                backgroundColor: getStatusColor(log.status),
                color: 'white'
              }}
            >
              {log.status}
            </Text>
            <Text className="text-sm text-gray-600">
              {isError ? 'ERROR' : 'RESPONSE'}
            </Text>
          </View>
        )}

        {isError && log.error && (
          <Text className="text-sm text-red-600" numberOfLines={2}>
            {typeof log.error === 'string' ? log.error : JSON.stringify(log.error)}
          </Text>
        )}
      </TouchableOpacity>
    );
  };

  const renderLogDetail = () => {
    if (!selectedLog) return null;

    return (
      <Modal
        visible={!!selectedLog}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View className="flex-1 bg-gray-50">
          <View className="bg-white border-b border-gray-200 px-4 py-3">
            <View className="flex-row items-center justify-between">
              <Text className="text-lg font-bold">Request Details</Text>
              <TouchableOpacity onPress={() => setSelectedLog(null)}>
                <Ionicons name="close" size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
          </View>

          <ScrollView className="flex-1 p-4">
            <View className="bg-white rounded-lg p-4 mb-4">
              <Text className="text-sm font-bold text-gray-700 mb-2">Basic Info</Text>
              <Text className="text-sm mb-1">
                <Text className="font-semibold">Type:</Text> {selectedLog.type}
              </Text>
              <Text className="text-sm mb-1">
                <Text className="font-semibold">Time:</Text> {selectedLog.timestamp}
              </Text>
              {selectedLog.duration && (
                <Text className="text-sm mb-1">
                  <Text className="font-semibold">Duration:</Text> {selectedLog.duration}
                </Text>
              )}
              {selectedLog.method && (
                <Text className="text-sm mb-1">
                  <Text className="font-semibold">Method:</Text> {selectedLog.method}
                </Text>
              )}
              {selectedLog.status && (
                <Text className="text-sm mb-1">
                  <Text className="font-semibold">Status:</Text> {selectedLog.status}
                </Text>
              )}
              {selectedLog.url && (
                <Text className="text-sm">
                  <Text className="font-semibold">URL:</Text> {selectedLog.url}
                </Text>
              )}
            </View>

            {selectedLog.headers && (
              <View className="bg-white rounded-lg p-4 mb-4">
                <Text className="text-sm font-bold text-gray-700 mb-2">Headers</Text>
                <Text className="text-xs font-mono text-gray-600">
                  {JSON.stringify(selectedLog.headers, null, 2)}
                </Text>
              </View>
            )}

            {selectedLog.body && (
              <View className="bg-white rounded-lg p-4 mb-4">
                <Text className="text-sm font-bold text-gray-700 mb-2">Request Body</Text>
                <Text className="text-xs font-mono text-gray-600">
                  {typeof selectedLog.body === 'string' 
                    ? selectedLog.body 
                    : JSON.stringify(selectedLog.body, null, 2)
                  }
                </Text>
              </View>
            )}

            {selectedLog.data && (
              <View className="bg-white rounded-lg p-4 mb-4">
                <Text className="text-sm font-bold text-gray-700 mb-2">Response Data</Text>
                <Text className="text-xs font-mono text-gray-600">
                  {JSON.stringify(selectedLog.data, null, 2)}
                </Text>
              </View>
            )}

            {selectedLog.error && (
              <View className="bg-white rounded-lg p-4 mb-4">
                <Text className="text-sm font-bold text-red-700 mb-2">Error</Text>
                <Text className="text-xs font-mono text-red-600">
                  {typeof selectedLog.error === 'string' 
                    ? selectedLog.error 
                    : JSON.stringify(selectedLog.error, null, 2)
                  }
                </Text>
              </View>
            )}
          </ScrollView>
        </View>
      </Modal>
    );
  };

  return (
    <>
      {/* Floating Debug Button */}
      <TouchableOpacity
        className="absolute bottom-20 right-4 bg-blue-600 rounded-full p-3 shadow-lg z-50"
        onPress={() => {
          setIsVisible(true);
          refreshLogs();
        }}
        style={{ elevation: 8 }}
      >
        <Ionicons name="bug" size={24} color="white" />
      </TouchableOpacity>

      {/* Debug Modal */}
      <Modal
        visible={isVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View className="flex-1 bg-gray-50">
          {/* Header */}
          <View className="bg-white border-b border-gray-200 px-4 py-3">
            <View className="flex-row items-center justify-between">
              <Text className="text-lg font-bold">Network Debugger</Text>
              <View className="flex-row items-center">
                <TouchableOpacity
                  className="mr-3 p-2"
                  onPress={() => setAutoRefresh(!autoRefresh)}
                >
                  <Ionicons 
                    name={autoRefresh ? "pause" : "play"} 
                    size={20} 
                    color={autoRefresh ? "#EF4444" : "#10B981"} 
                  />
                </TouchableOpacity>
                <TouchableOpacity className="mr-3 p-2" onPress={refreshLogs}>
                  <Ionicons name="refresh" size={20} color="#3B82F6" />
                </TouchableOpacity>
                <TouchableOpacity className="mr-3 p-2" onPress={exportLogs}>
                  <Ionicons name="share" size={20} color="#6B7280" />
                </TouchableOpacity>
                <TouchableOpacity className="mr-3 p-2" onPress={clearLogs}>
                  <Ionicons name="trash" size={20} color="#EF4444" />
                </TouchableOpacity>
                <TouchableOpacity onPress={() => setIsVisible(false)}>
                  <Ionicons name="close" size={24} color="#6B7280" />
                </TouchableOpacity>
              </View>
            </View>
            <Text className="text-sm text-gray-500 mt-1">
              {logs.length} requests • Auto-refresh: {autoRefresh ? 'ON' : 'OFF'}
            </Text>
          </View>

          {/* Logs List */}
          <ScrollView className="flex-1 p-4">
            {logs.length === 0 ? (
              <View className="flex-1 items-center justify-center">
                <Ionicons name="wifi-outline" size={48} color="#9CA3AF" />
                <Text className="text-gray-500 mt-4">No network requests yet</Text>
                <Text className="text-gray-400 text-sm mt-2">
                  Make some API calls to see them here
                </Text>
              </View>
            ) : (
              logs.map((log, index) => renderLogItem(log, index))
            )}
          </ScrollView>
        </View>
      </Modal>

      {renderLogDetail()}
    </>
  );
};

export default NetworkDebugger;
